// Example WebDriverIO capabilities for mobile automation
// Copy and modify this file according to your testing needs

// Android Native App capabilities
export const androidNativeCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554', // Your device name or emulator
  'appium:app': '/path/to/your/app.apk', // Path to your APK file
  'appium:automationName': 'UiAutomator2',
  'appium:newCommandTimeout': 300,
  'appium:connectHardwareKeyboard': true,
  'appium:autoGrantPermissions': true,
  'appium:noReset': false,
  'appium:fullReset': false,
  'appium:unicodeKeyboard': true,
  'appium:resetKeyboard': true,
  'appium:appWaitActivity': '*', // Wait for any activity
  'appium:appWaitDuration': 30000
};

// Android Real Device capabilities
export const androidRealDeviceCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'Samsung Galaxy S21', // Your real device name
  'appium:udid': 'your-device-udid', // Get from 'adb devices'
  'appium:app': '/path/to/your/app.apk',
  'appium:automationName': 'UiAutomator2',
  'appium:newCommandTimeout': 300,
  'appium:autoGrantPermissions': true,
  'appium:noReset': true, // Don't reset app state
  'appium:systemPort': 8201 // Use different port for multiple devices
};

// iOS Native App capabilities
export const iosNativeCapabilities = {
  platformName: 'iOS',
  'appium:deviceName': 'iPhone 14', // Your device name or simulator
  'appium:app': '/path/to/your/app.ipa', // Path to your IPA file
  'appium:automationName': 'XCUITest',
  'appium:newCommandTimeout': 300,
  'appium:connectHardwareKeyboard': true,
  'appium:autoAcceptAlerts': true,
  'appium:autoDismissAlerts': false,
  'appium:noReset': false,
  'appium:fullReset': false,
  'appium:wdaLaunchTimeout': 60000,
  'appium:wdaConnectionTimeout': 60000
};

// iOS Real Device capabilities
export const iosRealDeviceCapabilities = {
  platformName: 'iOS',
  'appium:deviceName': 'iPhone 14 Pro', // Your real device name
  'appium:udid': 'your-device-udid', // Get from Xcode or instruments
  'appium:app': '/path/to/your/app.ipa',
  'appium:automationName': 'XCUITest',
  'appium:newCommandTimeout': 300,
  'appium:xcodeOrgId': 'your-team-id',
  'appium:xcodeSigningId': 'iPhone Developer',
  'appium:updatedWDABundleId': 'com.yourcompany.WebDriverAgentRunner',
  'appium:noReset': true,
  'appium:wdaLaunchTimeout': 60000
};

// Android App Package/Activity capabilities (for installed apps)
export const androidInstalledAppCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:appPackage': 'com.yourcompany.yourapp', // App package name
  'appium:appActivity': '.MainActivity', // Main activity
  'appium:automationName': 'UiAutomator2',
  'appium:newCommandTimeout': 300,
  'appium:autoGrantPermissions': true,
  'appium:noReset': true // Don't reinstall app
};

// iOS Bundle ID capabilities (for installed apps)
export const iosInstalledAppCapabilities = {
  platformName: 'iOS',
  'appium:deviceName': 'iPhone 14',
  'appium:bundleId': 'com.yourcompany.yourapp', // App bundle ID
  'appium:automationName': 'XCUITest',
  'appium:newCommandTimeout': 300,
  'appium:noReset': true
};

// Web capabilities for mobile browsers
export const mobileWebCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:browserName': 'Chrome',
  'appium:automationName': 'UiAutomator2',
  'appium:newCommandTimeout': 300,
  'appium:chromeOptions': {
    'w3c': false,
    'args': ['--disable-web-security', '--allow-running-insecure-content']
  }
};

// Hybrid app capabilities (native + web views)
export const hybridAppCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:app': '/path/to/your/hybrid-app.apk',
  'appium:automationName': 'UiAutomator2',
  'appium:newCommandTimeout': 300,
  'appium:autoWebview': true, // Automatically switch to web view
  'appium:webviewConnectTimeout': 30000
};

// Remote Appium server configuration
export const remoteConfig = {
  hostname: 'localhost',
  port: 4723,
  path: '/wd/hub'
};

// Cloud service configurations (BrowserStack, Sauce Labs, etc.)
export const browserStackConfig = {
  hostname: 'hub-cloud.browserstack.com',
  port: 443,
  protocol: 'https',
  path: '/wd/hub',
  user: 'your-username',
  key: 'your-access-key'
};

export const sauceLabsConfig = {
  hostname: 'ondemand.us-west-1.saucelabs.com',
  port: 443,
  protocol: 'https',
  path: '/wd/hub',
  user: 'your-username',
  key: 'your-access-key'
};
