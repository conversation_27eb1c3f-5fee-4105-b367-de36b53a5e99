# Native Mobile App Testing Setup Guide

This guide covers the complete setup for native mobile app testing using AgentQ Mobile Automation Test with WebDriverIO and Appium.

## ✅ Native Mobile App Support

The current setup is **fully optimized** for native mobile app automation with the following features:

### 🎯 Native Mobile Features Supported

- **✅ Native App Launch/Close**: App lifecycle management
- **✅ Native Gestures**: Tap, long press, double tap, swipe, pinch, zoom
- **✅ Native Element Selection**: Accessibility IDs, resource IDs, XPath
- **✅ Device Interactions**: Keyboard, permissions, device rotation
- **✅ App State Management**: Background/foreground, app switching
- **✅ Native Form Controls**: Text fields, pickers, switches, sliders
- **✅ Platform-Specific Actions**: Android back button, iOS alerts

### 📱 Supported Platforms

- **Android**: Native apps (.apk), installed apps (package/activity)
- **iOS**: Native apps (.ipa), installed apps (bundle ID)
- **Real Devices**: Android and iOS physical devices
- **Emulators/Simulators**: Android emulators, iOS simulators
- **Hybrid Apps**: Native + WebView contexts

## 🚀 Quick Start for Native Apps

### 1. Prerequisites

```bash
# Install Appium globally
npm install -g appium

# Install mobile drivers
appium driver install uiautomator2  # For Android
appium driver install xcuitest      # For iOS

# Start Appium server
appium
```

### 2. Android Native App Example

```typescript
import { q, initBrowser, closeBrowser } from 'agentq_mobile_automation_test';

const androidCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:app': '/path/to/your/app.apk',
  'appium:automationName': 'UiAutomator2',
  'appium:autoGrantPermissions': true
};

async function testAndroidApp() {
  const browser = await initBrowser(androidCapabilities);
  
  try {
    await q("launch the app and wait for it to load");
    await q("tap on the login button");
    await q("enter '<EMAIL>' in email field");
    await q("hide the keyboard");
    await q("swipe down to scroll");
    await q("long press on profile picture");
  } finally {
    await closeBrowser();
  }
}
```

### 3. iOS Native App Example

```typescript
const iosCapabilities = {
  platformName: 'iOS',
  'appium:deviceName': 'iPhone 14',
  'appium:app': '/path/to/your/app.ipa',
  'appium:automationName': 'XCUITest',
  'appium:autoAcceptAlerts': true
};

async function testIOSApp() {
  const browser = await initBrowser(iosCapabilities);
  
  try {
    await q("launch the app");
    await q("allow location permissions if prompted");
    await q("tap on get started button");
    await q("swipe left to see next tutorial screen");
    await q("double tap on image to zoom");
  } finally {
    await closeBrowser();
  }
}
```

## 🔧 Native Mobile Commands

### App Lifecycle
- `q("launch the app")`
- `q("close the app")`
- `q("background the app for 5 seconds")`
- `q("activate the app")`

### Native Gestures
- `q("tap on login button")`
- `q("long press on menu item")`
- `q("double tap on image")`
- `q("swipe left/right/up/down")`
- `q("pinch to zoom out")`
- `q("zoom in on map")`

### Device Interactions
- `q("hide the keyboard")`
- `q("rotate device to landscape")`
- `q("press back button")` (Android)
- `q("press home button")`

### Permissions & Alerts
- `q("allow camera permissions")`
- `q("dismiss notification")`
- `q("accept location alert")` (iOS)

## 📋 Native Element Selectors

### Recommended: Accessibility IDs
```typescript
// Works on both Android and iOS
const loginButton = $('~loginButton');
const usernameField = $('~usernameField');
```

### Android Specific
```typescript
// Resource ID
const field = $('android=new UiSelector().resourceId("com.app:id/username")');

// Text content
const button = $('android=new UiSelector().text("Submit")');

// Class name
const editText = $('android.widget.EditText');
```

### iOS Specific
```typescript
// Predicate string
const field = $('-ios predicate string:name == "username"');

// Class chain
const button = $('-ios class chain:**/XCUIElementTypeButton[`name == "Submit"`]');
```

## 🎛️ Capability Configurations

### Android Real Device
```javascript
{
  platformName: 'Android',
  'appium:deviceName': 'Samsung Galaxy S21',
  'appium:udid': 'device-udid-from-adb-devices',
  'appium:app': '/path/to/app.apk',
  'appium:automationName': 'UiAutomator2',
  'appium:autoGrantPermissions': true,
  'appium:systemPort': 8201
}
```

### iOS Real Device
```javascript
{
  platformName: 'iOS',
  'appium:deviceName': 'iPhone 14 Pro',
  'appium:udid': 'device-udid-from-xcode',
  'appium:app': '/path/to/app.ipa',
  'appium:automationName': 'XCUITest',
  'appium:xcodeOrgId': 'your-team-id',
  'appium:xcodeSigningId': 'iPhone Developer'
}
```

### Installed Apps
```javascript
// Android - using package/activity
{
  platformName: 'Android',
  'appium:appPackage': 'com.yourcompany.yourapp',
  'appium:appActivity': '.MainActivity',
  'appium:automationName': 'UiAutomator2',
  'appium:noReset': true
}

// iOS - using bundle ID
{
  platformName: 'iOS',
  'appium:bundleId': 'com.yourcompany.yourapp',
  'appium:automationName': 'XCUITest',
  'appium:noReset': true
}
```

## 🔍 Debugging Native Apps

### Element Inspection
```bash
# Android - use uiautomatorviewer or Appium Inspector
# iOS - use Appium Inspector or Xcode Accessibility Inspector
```

### Common Issues & Solutions

1. **Element Not Found**
   - Use Appium Inspector to find correct selectors
   - Try accessibility ID instead of XPath
   - Add wait conditions: `await element.waitForDisplayed()`

2. **App Doesn't Launch**
   - Check app path is correct
   - Verify device/emulator is connected
   - Check Appium server logs

3. **Permissions Issues**
   - Use `'appium:autoGrantPermissions': true` for Android
   - Use `'appium:autoAcceptAlerts': true` for iOS

4. **Slow Performance**
   - Increase timeouts: `'appium:newCommandTimeout': 300`
   - Use `'appium:noReset': true` to avoid app reinstall

## 📚 Examples

Check the `examples/` directory for comprehensive examples:
- `native-mobile-app-example.ts` - Complete native app testing
- `mixed-approach-example.ts` - AI + direct WebDriverIO usage

## 🎉 Summary

The current setup is **100% ready** for native mobile app automation with:
- ✅ Full native gesture support
- ✅ Platform-specific element selectors
- ✅ App lifecycle management
- ✅ Device interaction capabilities
- ✅ Real device and emulator support
- ✅ Comprehensive error handling

You can now test native Android and iOS apps using natural language commands through AgentQ AI!
