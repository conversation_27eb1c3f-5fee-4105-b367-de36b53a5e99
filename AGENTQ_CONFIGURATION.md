# AgentQ Library Configuration

The AgentQ library now uses **environment variables only** and no longer requires the `agentq.config.json` file.

## Environment Variables

### Required Variables

```bash
# API Token for AgentQ service
AGENTQ_TOKEN=your_api_token_here

# Alternative token variable names (for compatibility)
VITE_AGENTQ_API_KEY=your_api_token_here
```

### Optional Variables

```bash
# WebSocket Service URL (auto-detected based on environment)
AGENTQ_SERVICE_URL=ws://localhost:3008  # for development
# AGENTQ_SERVICE_URL=wss://websocket-ai-automation-test-api.agentq.id  # for production

# Alternative service URL variable names (for compatibility)
VITE_WEBSOCKET_URL=ws://localhost:3008

# Environment detection
NODE_ENV=development  # or 'production'
AGENTQ_ENV=development  # or 'production'
```

## Automatic Environment Detection

The library automatically detects the environment and uses appropriate defaults:

### Development Mode (any of these conditions):
- `NODE_ENV=development`
- `AGENTQ_ENV=development` 
- Service URL contains `localhost`

**Default WebSocket URL**: `ws://localhost:3008`

### Production Mode:
- When none of the development conditions are met

**Default WebSocket URL**: `wss://websocket-ai-automation-test-api.agentq.id`

## Usage Examples

### Development Setup (.env file)
```bash
# Development environment
NODE_ENV=development
AGENTQ_TOKEN=0305df7c7934ee7d7dfc0bc0ece1442a1ecd3bab772ecf7a21d4a01e34471142
AGENTQ_SERVICE_URL=ws://localhost:3008
```

### Production Setup (.env file)
```bash
# Production environment  
NODE_ENV=production
AGENTQ_TOKEN=your_production_token_here
# AGENTQ_SERVICE_URL will default to wss://websocket-ai-automation-test-api.agentq.id
```

### Using in Code
```javascript
import { getConfig, getServiceUrl, getToken } from 'agentq_web_automation_test';

// Get full configuration
const config = getConfig();
console.log('Token:', config.TOKEN);
console.log('Service URL:', config.SERVICE_URL);

// Get individual values
const serviceUrl = getServiceUrl();
const token = getToken();
```

## Migration from agentq.config.json

If you were previously using `agentq.config.json`, simply move the values to environment variables:

**Old way (agentq.config.json):**
```json
{
  "TOKEN": "your_token",
  "SERVICE_URL": "ws://localhost:3008"
}
```

**New way (.env file):**
```bash
AGENTQ_TOKEN=your_token
AGENTQ_SERVICE_URL=ws://localhost:3008
```

## Benefits

✅ **No config files** - Everything through environment variables  
✅ **Environment-aware** - Automatic dev/prod detection  
✅ **Secure** - Sensitive data in environment variables  
✅ **Flexible** - Multiple variable name support for compatibility  
✅ **Simple deployment** - Just set environment variables  

## Troubleshooting

### Issue: Library uses wrong WebSocket URL
**Solution**: Set `AGENTQ_SERVICE_URL` explicitly or check `NODE_ENV`/`AGENTQ_ENV`

### Issue: Authentication fails
**Solution**: Verify `AGENTQ_TOKEN` is set correctly

### Issue: Can't connect to WebSocket
**Solution**: Check if the WebSocket server is running on the expected URL
