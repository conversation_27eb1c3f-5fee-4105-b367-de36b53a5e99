#!/bin/bash

# Debug test script for mobile automation
echo "🚀 Starting debug mobile test..."

# Clean up previous test artifacts
rm -rf client-apps/*

# Set test data
export TEST_CASE_ID="debug-test-001"
export TEST_DATA='{
  "testCaseId": "debug-test-001",
  "tcId": "1",
  "steps": [
    {
      "step": 1,
      "stepName": "Setup",
      "Actions": "[{\"action\":\"setup\",\"target\":\"\",\"value\":\"Android.SauceLabs.Mobile.Sample.app.2.7.1.apk\",\"fileUrl\":\"gs://agentq/test-data/mobile-app-files/f907b2b1-4347-480c-8bc5-0b669649599a/1755937959527-Android.SauceLabs.Mobile.Sample.app.2.7.1.apk\",\"deviceId\":\"0d625166-303c-5978-a9c0-302874e6feb4\",\"deviceName\":\"Medium_Phone_API_36.0\",\"platform\":\"android\",\"version\":\"16\"}]"
    },
    {
      "step": 2,
      "stepName": "Fill Username",
      "Actions": "[{\"action\":\"write\",\"target\":\"~test-Username\",\"value\":\"standard_user\"}]"
    }
  ]
}'

echo "📱 Test data configured"
echo "🔧 Building project..."

# Build the project
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build completed"
echo "🧪 Running debug test..."

# Run the debug test
npx wdio run wdio.conf.ts --spec tests/debug-simple.spec.ts

echo "🏁 Debug test execution completed"
