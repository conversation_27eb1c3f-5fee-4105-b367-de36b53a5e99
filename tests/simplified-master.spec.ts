/**
 * Simplified Master Test Spec
 * Based on the working appium_wdio_ts sample
 * This bypasses the agentq_mobile_automation_test library issues
 */

import { CapabilityFactory } from '../src/services/capability-factory';
import { executeSimplifiedMobileTest } from './simplified-mobile-test';

describe('Simplified Mobile Test Execution', () => {
  it('should execute mobile test steps using direct WebDriverIO', async function() {
    this.timeout(300000); // 5 minutes timeout

    try {
      console.log('🚀 Starting simplified mobile test execution');

      // Get test data from environment
      const testData = process.env.TEST_DATA;
      if (!testData) {
        throw new Error('TEST_DATA environment variable is required');
      }

      const stepsData = JSON.parse(testData);
      console.log(`📱 Loaded test data with ${stepsData.steps.length} steps`);

      // Find setup action
      let setupAction = null;
      let appPath = '';

      for (const step of stepsData.steps) {
        const actions = JSON.parse(step.Actions);
        const setup = actions.find((action: any) => action.action === 'setup');
        if (setup) {
          setupAction = setup;
          break;
        }
      }

      if (!setupAction) {
        throw new Error('No setup action found in test steps');
      }

      // console.log('📱 Found setup action, configuring mobile capabilities');
      // console.log(`Platform: ${setupAction.platform}, Device: ${setupAction.deviceName}, Version: ${setupAction.version}`);

      // Generate capabilities using your existing factory
      const dynamicCapabilities = CapabilityFactory.createCapabilities(
        stepsData.steps,
        stepsData.testCaseId
      );

      console.log('🔧 Generated capabilities:', JSON.stringify(dynamicCapabilities, null, 2));

      // Extract app path from capabilities
      if (dynamicCapabilities.capabilities && dynamicCapabilities.capabilities['appium:app']) {
        appPath = dynamicCapabilities.capabilities['appium:app'];
        console.log(`📱 App path: ${appPath}`);
      } else {
        throw new Error('App path not found in capabilities');
      }

      // Execute simplified mobile test using existing browser session
      await executeSimplifiedMobileTest(
        stepsData.testCaseId,
        stepsData.steps
      );

      console.log('✅ Simplified mobile test completed successfully');

    } catch (error) {
      console.error('❌ Simplified mobile test failed:', error);
      throw error;
    }
  });
});
