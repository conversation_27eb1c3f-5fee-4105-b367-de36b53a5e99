/**
 * Minimal Test - Find exactly where execution stops
 */

describe('Minimal Test', () => {
  it('should identify where execution stops', async function() {
    this.timeout(300000);

    console.log('🚀 STEP 1: Test started');

    try {
      // Use the existing browser session from wdio.conf.ts
      console.log('🚀 STEP 2: Using existing browser session');
      console.log(`🔍 Browser object exists: ${typeof browser !== 'undefined'}`);
      
      if (typeof browser !== 'undefined') {
        console.log('🚀 STEP 3: Browser object available');
        console.log(`🔍 Session ID: ${browser.sessionId}`);
        
        console.log('🚀 STEP 4: Attempting to get page source...');
        const source = await browser.getPageSource();
        console.log(`🚀 STEP 5: Got page source (${source.length} chars)`);
        
        console.log('🚀 STEP 6: Looking for username element...');
        const usernameElement = browser.$('~test-Username');
        console.log('🚀 STEP 7: Got username element reference');
        
        console.log('🚀 STEP 8: Checking if username element exists...');
        const exists = await usernameElement.isExisting();
        console.log(`🚀 STEP 9: Username element exists: ${exists}`);
        
        if (exists) {
          console.log('🚀 STEP 10: Username element exists, trying to fill...');
          await usernameElement.setValue('standard_user');
          console.log('🚀 STEP 11: ✅ USERNAME FILLED SUCCESSFULLY!');
          
          console.log('🚀 STEP 12: Looking for password element...');
          const passwordElement = browser.$('~test-Password');
          const passwordExists = await passwordElement.isExisting();
          console.log(`🚀 STEP 13: Password element exists: ${passwordExists}`);
          
          if (passwordExists) {
            console.log('🚀 STEP 14: Filling password...');
            await passwordElement.setValue('secret_sauce');
            console.log('🚀 STEP 15: ✅ PASSWORD FILLED SUCCESSFULLY!');
            
            console.log('🚀 STEP 16: Looking for login button...');
            const loginElement = browser.$('~test-LOGIN');
            const loginExists = await loginElement.isExisting();
            console.log(`🚀 STEP 17: Login button exists: ${loginExists}`);
            
            if (loginExists) {
              console.log('🚀 STEP 18: Clicking login button...');
              await loginElement.click();
              console.log('🚀 STEP 19: ✅ LOGIN CLICKED SUCCESSFULLY!');
              
              console.log('🚀 STEP 20: Waiting 3 seconds...');
              await browser.pause(3000);
              console.log('🚀 STEP 21: ✅ ALL STEPS COMPLETED!');
            }
          }
        } else {
          console.log('❌ Username element does not exist');
          
          // Try to find any elements
          console.log('🔍 Looking for any elements...');
          const allElements = await browser.$$('*');
          console.log(`📱 Found ${allElements.length} total elements`);
          
          // Try different selectors
          console.log('🔍 Trying different selectors...');
          const byId = await browser.$('#test-Username');
          const byIdExists = await byId.isExisting();
          console.log(`ID selector exists: ${byIdExists}`);
          
          const byXpath = await browser.$('//android.widget.EditText[@content-desc="test-Username"]');
          const byXpathExists = await byXpath.isExisting();
          console.log(`XPath selector exists: ${byXpathExists}`);
        }
      } else {
        console.log('❌ Browser object not available');
      }
      
      console.log('🎉 Test completed successfully');
      
    } catch (error) {
      console.error('❌ Test failed at some step:', error);
      if (error instanceof Error) {
        console.error('Stack trace:', error.stack);
      }
      throw error;
    }
  });
});
