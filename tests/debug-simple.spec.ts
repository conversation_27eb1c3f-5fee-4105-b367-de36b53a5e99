/**
 * Debug Simple Test - Just test basic WebDriverIO functionality
 */

import { CapabilityFactory } from '../src/services/capability-factory';
import { remote } from 'webdriverio';

describe('Debug Simple Test', () => {
  it('should create session and perform basic actions', async function() {
    this.timeout(300000); // 5 minutes timeout

    let browser: any = null;

    try {
      console.log('🚀 Starting debug simple test');

      // Get test data from environment
      const testData = process.env.TEST_DATA;
      if (!testData) {
        throw new Error('TEST_DATA environment variable is required');
      }

      const stepsData = JSON.parse(testData);
      console.log(`📱 Loaded test data with ${stepsData.steps.length} steps`);

      // Generate capabilities
      const dynamicCapabilities = CapabilityFactory.createCapabilities(
        stepsData.steps,
        stepsData.testCaseId
      );

      console.log('🔧 Generated capabilities');

      // Extract app path
      const appPath = dynamicCapabilities.capabilities['appium:app'];
      console.log(`📱 App path: ${appPath}`);

      // Create simple session with timeout
      console.log('🔗 Creating WebDriverIO session...');
      console.log('⏳ This should complete within 30 seconds...');

      const sessionStart = Date.now();

      browser = await Promise.race([
        remote({
          hostname: 'localhost',
          port: 4723,
          path: '/wd/hub',
          logLevel: 'info',
          capabilities: {
            platformName: 'Android',
            'appium:platformVersion': '16.0',
            'appium:deviceName': 'Medium_Phone_API_36.0',
            'appium:app': appPath,
            'appium:automationName': 'UiAutomator2',
            'appium:newCommandTimeout': 300,
            'appium:autoGrantPermissions': true,
            'df:accesskey': 'admin_AzZvGSzPbIrrEx',
            'df:token': '9de28a88-21b5-4887-8282-848106f3afe5'
          } as any
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Session creation timeout after 45 seconds')), 45000)
        )
      ]) as any;

      const sessionTime = Date.now() - sessionStart;
      console.log(`✅ Session created successfully in ${sessionTime}ms`);
      console.log(`🔍 Session ID: ${browser.sessionId}`);
      console.log('🎯 THIS IS THE CRITICAL POINT - if you see this, session creation worked!');

      // Immediate check - no waiting
      console.log('🔍 IMMEDIATE CHECK: Getting page source right after session...');
      try {
        const immediateSource = await browser.getPageSource();
        console.log(`📱 Immediate page source length: ${immediateSource.length} characters`);
        console.log('✅ GOOD: Browser object is working immediately!');
      } catch (immediateError) {
        console.error('❌ PROBLEM: Browser object not working immediately:', immediateError);
      }

      // Wait for app to load
      console.log('⏱️ Waiting 5 seconds for native app to fully load...');
      await browser.pause(5000);

      // Try to get page source again
      console.log('🔍 Getting page source after wait...');
      const source = await browser.getPageSource();
      console.log(`📱 Page source length after wait: ${source.length} characters`);

      // Check if login elements exist
      console.log('🔍 Looking for native app login elements...');
      console.log('🎯 Testing accessibility ID selectors for native app...');

      try {
        console.log('🔍 Searching for ~test-Username...');
        const usernameElement = await browser.$('~test-Username');
        console.log('✅ Found username element object');

        const isUsernameDisplayed = await usernameElement.isDisplayed();
        console.log(`📱 Username field displayed: ${isUsernameDisplayed}`);
        
        if (isUsernameDisplayed) {
          console.log('⌨️ Attempting to fill username...');
          await usernameElement.setValue('standard_user');
          console.log('✅ Username filled successfully!');
          
          // Try password field
          const passwordElement = await browser.$('~test-Password');
          const isPasswordDisplayed = await passwordElement.isDisplayed();
          console.log(`📱 Password field displayed: ${isPasswordDisplayed}`);
          
          if (isPasswordDisplayed) {
            console.log('⌨️ Attempting to fill password...');
            await passwordElement.setValue('secret_sauce');
            console.log('✅ Password filled successfully!');
            
            // Try login button
            const loginElement = await browser.$('~test-LOGIN');
            const isLoginDisplayed = await loginElement.isDisplayed();
            console.log(`📱 Login button displayed: ${isLoginDisplayed}`);
            
            if (isLoginDisplayed) {
              console.log('🖱️ Attempting to click login...');
              await loginElement.click();
              console.log('✅ Login clicked successfully!');
              
              // Wait and check result
              await browser.pause(3000);
              console.log('🎉 Login test completed!');
            }
          }
        }
      } catch (elementError) {
        console.error('❌ Element interaction failed:', elementError);
        
        // Fallback: try to find any elements
        console.log('🔍 Trying to find any elements...');
        const allElements = await browser.$$('*');
        console.log(`📱 Found ${allElements.length} elements on screen`);
        
        // Print first few elements for debugging
        for (let i = 0; i < Math.min(5, allElements.length); i++) {
          try {
            const tagName = await allElements[i].getTagName();
            const text = await allElements[i].getText();
            console.log(`Element ${i}: ${tagName} - "${text}"`);
          } catch (e) {
            console.log(`Element ${i}: Could not get details`);
          }
        }
      }

      console.log('✅ Debug test completed successfully');

    } catch (error) {
      console.error('❌ Debug test failed:', error);
      throw error;
    } finally {
      if (browser) {
        console.log('🔚 Closing browser session...');
        try {
          await browser.deleteSession();
          console.log('✅ Browser session closed');
        } catch (closeError) {
          console.error('⚠️ Error closing browser session:', closeError);
        }
      }
    }
  });
});
