/**
 * Simplified Mobile Test Execution
 * Based on the working appium_wdio_ts sample
 * This bypasses the agentq_mobile_automation_test library and uses direct WebDriverIO
 */

// No imports needed - using global browser object from WebDriverIO

interface TestStep {
  step: number;
  stepName: string;
  Actions: string;
}

interface TestAction {
  action: string;
  target: string;
  value: string;
  platform?: string;
  deviceName?: string;
  deviceId?: string;
  udid?: string;
  version?: string;
  fileUrl?: string;
  prompt?: string;
}

/**
 * Simplified mobile test execution that works like the appium_wdio_ts sample
 */
export async function executeSimplifiedMobileTest(
  testCaseId: string,
  steps: TestStep[]
) {
  console.log(`🚀 Starting simplified mobile test: ${testCaseId}`);
  console.log(`📱 Using existing browser session from wdio.conf.ts`);

  // Use the global browser object instead of creating a new session
  if (typeof browser === 'undefined') {
    throw new Error('Browser session not available - make sure this runs within WebDriverIO context');
  }

  try {
    console.log('✅ Using existing WebDriverIO session');
    console.log(`🔍 Session ID: ${browser.sessionId}`);

    console.log('✅ WebDriverIO session created successfully');
    console.log(`🔍 Session ID: ${browser.sessionId}`);
    console.log(`🔍 Browser capabilities: ${JSON.stringify(browser.capabilities, null, 2)}`);

    console.log('⏱️ Waiting 5 seconds for app to fully load...');
    await browser.pause(5000);

    // Check if app is actually loaded
    console.log('🔍 Checking if app is loaded...');
    try {
      const source = await browser.getPageSource();
      console.log(`📱 App loaded, page source length: ${source.length} characters`);
      if (source.includes('test-Username') || source.includes('Username')) {
        console.log('✅ Login screen detected in page source');
      } else {
        console.log('⚠️ Login screen not detected, app might still be loading');
      }
    } catch (error) {
      console.log('⚠️ Could not get page source:', error);
    }

    // Start mobile video recording now that app is loaded
    try {
      console.log('🎥 Starting mobile video recording after app is ready...');
      const { spawn } = require('child_process');
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const videoFileName = `/sdcard/mobile_test_${testCaseId.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.mp4`;

      const videoProcess = spawn('adb', [
        '-s', 'emulator-5554',
        'shell', 'screenrecord',
        '--verbose',
        '--bit-rate', '4000000',
        '--size', '720x1280',
        videoFileName
      ]);

      videoProcess.on('error', (error: any) => {
        console.error('❌ Mobile video recording error:', error);
      });

      console.log(`✅ Mobile video recording started: ${videoFileName}`);

      // Store video info globally for cleanup later
      (global as any).mobileVideoInfo = {
        process: videoProcess,
        fileName: videoFileName,
        testCaseId: testCaseId
      };
    } catch (error) {
      console.warn('⚠️ Could not start mobile video recording:', error);
    }

    // Execute test steps (skip setup step)
    console.log('🔍 Filtering test steps...');
    const testSteps = steps.filter(step => {
      const actions = JSON.parse(step.Actions);
      return !actions.some((action: TestAction) => action.action === 'setup');
    });

    console.log(`📋 ===== STARTING TEST STEP EXECUTION =====`);
    console.log(`📋 Total steps: ${steps.length}, Test steps (excluding setup): ${testSteps.length}`);
    console.log(`🔍 Test steps to execute:`, testSteps.map(s => `Step ${s.step}: ${s.stepName}`));

    for (const step of testSteps) {
      console.log(`\n🔥 ===== EXECUTING STEP ${step.step}: ${step.stepName} =====`);
      console.log(`🔍 Step data:`, JSON.stringify(step, null, 2));

      const actions: TestAction[] = JSON.parse(step.Actions);
      console.log(`🔍 Actions in this step: ${actions.length}`);
      
      for (const action of actions) {
        console.log(`🎯 Action: ${action.action}, Target: ${action.target}, Value: ${action.value}`);
        
        if (action.action === 'write' && action.target && action.value) {
          console.log(`⌨️ Writing "${action.value}" to element "${action.target}"`);
          
          try {
            // Use accessibility ID selector (like working sample)
            const element = browser.$(`~${action.target.replace('~', '')}`);
            
            console.log('⏳ Waiting for element to exist...');
            await element.waitForExist({ timeout: 10000 });
            
            console.log('✅ Element found, setting value...');
            await element.setValue(action.value);
            
            console.log(`✅ Successfully wrote "${action.value}" to "${action.target}"`);
          } catch (error) {
            console.error(`❌ Failed to write to element "${action.target}":`, error);
            throw error;
          }
          
        } else if (action.action === 'click' && action.target) {
          console.log(`🖱️ Clicking element "${action.target}"`);
          
          try {
            // Use accessibility ID selector (like working sample)
            const element = browser.$(`~${action.target.replace('~', '')}`);
            
            console.log('⏳ Waiting for element to exist...');
            await element.waitForExist({ timeout: 10000 });
            
            console.log('✅ Element found, clicking...');
            await element.click();
            
            console.log(`✅ Successfully clicked "${action.target}"`);
          } catch (error) {
            console.error(`❌ Failed to click element "${action.target}":`, error);
            throw error;
          }
          
        } else if (action.action === 'prompt') {
          console.log(`💬 Prompt: ${action.value || action.prompt}`);
          // Handle prompt actions if needed
          
        } else {
          console.log(`⚠️ Unknown action: ${action.action}`);
        }
        
        // Small delay between actions
        await browser.pause(1000);
      }
    }

    console.log('\n🎉 All test steps completed successfully!');

    // Stop mobile video recording and pull the file
    try {
      const videoInfo = (global as any).mobileVideoInfo;
      if (videoInfo && videoInfo.process) {
        console.log('🛑 Stopping mobile video recording...');

        // Stop the recording process
        videoInfo.process.kill('SIGINT');

        // Wait for the recording to finalize
        await browser.pause(3000);

        // Create output directory
        const outputDir = process.env.CLIENT_ID ? `test-results/${process.env.CLIENT_ID}` : 'test-results';
        const fs = require('fs');
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir, { recursive: true });
        }

        // Generate local filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const path = require('path');
        const localVideoPath = path.join(outputDir, `mobile_video_${videoInfo.testCaseId.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.mp4`);

        console.log('📥 Pulling mobile video from device...');

        // Pull the video file from device
        const { spawn } = require('child_process');
        const pullProcess = spawn('adb', [
          '-s', 'emulator-5554',
          'pull', videoInfo.fileName, localVideoPath
        ]);

        await new Promise((resolve) => {
          pullProcess.on('close', (code: number) => {
            if (code === 0) {
              console.log(`✅ Mobile video saved: ${localVideoPath}`);

              // Clean up the file from device
              spawn('adb', ['-s', 'emulator-5554', 'shell', 'rm', videoInfo.fileName]);
            } else {
              console.error(`❌ Failed to pull mobile video, exit code: ${code}`);
            }
            resolve(undefined);
          });
        });

        // Clear global video info
        (global as any).mobileVideoInfo = null;
      }
    } catch (error) {
      console.warn('⚠️ Could not stop mobile video recording:', error);
    }

    // Additional verification - try to get current activity
    try {
      const currentActivity = await browser.getCurrentActivity();
      console.log(`📱 Current activity after test: ${currentActivity}`);
    } catch (error) {
      console.log('⚠️ Could not get current activity:', error);
    }
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    throw error;
    
  } finally {
    if (browser) {
      console.log('🔚 Closing browser session...');
      try {
        await browser.deleteSession();
        console.log('✅ Browser session closed');
      } catch (closeError) {
        console.error('⚠️ Error closing browser session:', closeError);
      }
    }
  }
}
