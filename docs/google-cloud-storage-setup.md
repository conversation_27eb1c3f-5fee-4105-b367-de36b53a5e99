# Google Cloud Storage Integration

This document explains how to set up Google Cloud Storage integration for downloading files during test execution.

## Prerequisites

1. A Google Cloud Project with Cloud Storage API enabled
2. A service account with appropriate permissions to read from your storage buckets
3. Service account credentials (JSON key file or environment variables)

## Setup Options

### Option 1: Service Account Key File (Recommended for local development)

1. Create a service account in your Google Cloud Console
2. Download the JSON key file
3. Set the environment variable:
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account-key.json"
   ```

### Option 2: Environment Variables (Recommended for production)

Set the following environment variables:

```bash
export GOOGLE_CLOUD_PROJECT_ID="your-project-id"
export GOOGLE_CLOUD_CLIENT_EMAIL="<EMAIL>"
export GOOGLE_CLOUD_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"
```

## How It Works

When your test data contains an upload step with a `fileUrl` that starts with `gs://`, the test will:

1. Parse the Google Cloud Storage URL
2. Download the file to a local `downloads` directory
3. Use the local file path for the upload action

## Example Test Data

```json
{
  "step": 2,
  "stepName": "user upload file",
  "target": "input[name=\"file\"]",
  "value": "Screenshot 2025-08-07 at 20.40.25.png",
  "action": "upload",
  "fileUrl": "gs://agentq/test-data/automation-files/f907b2b1-4347-480c-8bc5-0b669649599a/********-4840-4fda-902d-197efffe50aa/step-2/********-4840-4fda-902d-197efffe50aa-step-2-*************-Screenshot 2025-08-07 at 20.40.25.png"
}
```

## Required Permissions

Your service account needs the following IAM permissions:

- `storage.objects.get` - To download files
- `storage.buckets.get` - To access bucket information

You can assign the `Storage Object Viewer` role which includes these permissions.

## Troubleshooting

1. **Authentication Error**: Ensure your credentials are properly set up
2. **Permission Denied**: Check that your service account has the required permissions
3. **File Not Found**: Verify the GCS URL is correct and the file exists
4. **Network Issues**: Ensure your environment can reach Google Cloud Storage APIs

## Local Downloads Directory

Downloaded files are stored in a `downloads` directory in your project root. This directory is created automatically if it doesn't exist.
