# AgentQ Mobile Automation Test

NPM library for low-code mobile automation testing using WebDriverIO and AgentQ AI. This library enables natural language-driven mobile app testing through AI-powered commands.

## Features

- 🤖 AI-powered test automation using natural language commands
- 📱 **Native Mobile App Testing** for Android and iOS
- 🔧 WebDriverIO + Appium automation framework
- 🌐 WebSocket communication with AgentQ AI service
- 📊 Automatic test result reporting
- 🎯 **Comprehensive Mobile Gestures**: tap, swipe, scroll, pinch, zoom, long press
- 📲 **Native App Features**: app launch/close, background/foreground, permissions
- 🔍 **Native Element Selection**: accessibility IDs, XPath, resource IDs
- 🎨 **Hybrid App Support**: seamless web view and native context switching
- ⚡ **Real Device & Emulator Support**: local and cloud testing

## Installation

```bash
npm install agentq_mobile_automation_test
```

## Prerequisites

1. **Appium Server**: Install and start Appium
```bash
npm install -g appium
appium
```

2. **Mobile Drivers**: Install required drivers
```bash
# For Android
appium driver install uiautomator2

# For iOS
appium driver install xcuitest
```

3. **Environment Variables**: Set up your AgentQ credentials
```bash
export AGENTQ_TOKEN="your-agentq-token"
export AGENTQ_SERVICE_URL="wss://websocket-ai-automation-test-api.agentq.id"
```

## Quick Start

### Native Mobile App Testing

```typescript
import { q, initBrowser, closeBrowser } from 'agentq_mobile_automation_test';

// Native Android app capabilities
const androidCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:app': '/path/to/your/app.apk', // Path to your APK
  'appium:automationName': 'UiAutomator2',
  'appium:autoGrantPermissions': true,
  'appium:noReset': false
};

// Native iOS app capabilities
const iosCapabilities = {
  platformName: 'iOS',
  'appium:deviceName': 'iPhone 14',
  'appium:app': '/path/to/your/app.ipa', // Path to your IPA
  'appium:automationName': 'XCUITest',
  'appium:autoAcceptAlerts': true
};

async function nativeAppTest() {
  const browser = await initBrowser(androidCapabilities);

  try {
    // Native mobile gestures and interactions
    await q("launch the app and wait for it to load");
    await q("tap on the login button");
    await q("enter '<EMAIL>' in the email field");
    await q("enter 'password123' in the password field");
    await q("hide the keyboard");
    await q("tap on submit button");
    await q("swipe left to navigate to next screen");
    await q("long press on the profile picture");
    await q("verify that welcome message is displayed");
  } finally {
    await closeBrowser();
  }
}

nativeAppTest().catch(console.error);
```

### Using Test Framework

```typescript
import { q, runTest } from 'agentq_mobile_automation_test';

const capabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:app': '/path/to/your/app.apk',
  'appium:automationName': 'UiAutomator2'
};

runTest('001-Login Test', capabilities, async ({ browser }) => {
  await q("open the app");
  await q("tap on login button");
  await q("enter username 'testuser'");
  await q("enter password 'testpass'");
  await q("tap submit button");
  await q("verify login success message appears");
}).catch(console.error);
```

### Using WebDriverIO Elements Directly

```typescript
import { q, initBrowser, $, $$, getBrowser, Browser } from 'agentq_mobile_automation_test';

const capabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:app': '/path/to/your/app.apk',
  'appium:automationName': 'UiAutomator2'
};

async function mixedApproach() {
  const browser = await initBrowser(capabilities);

  try {
    // Use AI commands
    await q("open the app");

    // Use direct WebDriverIO element access
    const loginButton = $('~loginButton');
    await loginButton.waitForDisplayed();
    await loginButton.click();

    // Mix AI and direct element access
    const usernameField = $('#username');
    await usernameField.setValue('testuser');

    await q("enter password 'testpass'");
    await q("tap submit button");

    // Direct assertion with element
    const welcomeMessage = $('~welcomeMessage');
    await welcomeMessage.waitForDisplayed();
    const text = await welcomeMessage.getText();
    console.log('Welcome message:', text);

  } finally {
    await closeBrowser();
  }
}
```

## Configuration

### Native Mobile App Capabilities

See `example-capabilities.js` for detailed configuration examples:

#### Android Native Apps
```javascript
const androidNativeCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:app': '/path/to/your/app.apk',
  'appium:automationName': 'UiAutomator2',
  'appium:autoGrantPermissions': true,
  'appium:noReset': false
};
```

#### iOS Native Apps
```javascript
const iosNativeCapabilities = {
  platformName: 'iOS',
  'appium:deviceName': 'iPhone 14',
  'appium:app': '/path/to/your/app.ipa',
  'appium:automationName': 'XCUITest',
  'appium:autoAcceptAlerts': true
};
```

#### Installed Apps (Package/Bundle ID)
```javascript
// Android installed app
const androidInstalledApp = {
  platformName: 'Android',
  'appium:appPackage': 'com.yourcompany.yourapp',
  'appium:appActivity': '.MainActivity',
  'appium:automationName': 'UiAutomator2'
};

// iOS installed app
const iosInstalledApp = {
  platformName: 'iOS',
  'appium:bundleId': 'com.yourcompany.yourapp',
  'appium:automationName': 'XCUITest'
};
```

### Native Element Selectors

For direct WebDriverIO element access in native apps:

#### Android Selectors
```typescript
// Accessibility ID (recommended)
const loginButton = $('~loginButton');

// Resource ID
const usernameField = $('android=new UiSelector().resourceId("com.app:id/username")');

// Text content
const submitButton = $('android=new UiSelector().text("Submit")');

// XPath
const specificButton = $('//android.widget.Button[@text="Login"]');
```

#### iOS Selectors
```typescript
// Accessibility ID (recommended)
const loginButton = $('~loginButton');

// Predicate string
const usernameField = $('-ios predicate string:name == "username"');

// Class chain
const submitButton = $('-ios class chain:**/XCUIElementTypeButton[`name == "Submit"`]');

// XPath
const specificButton = $('//XCUIElementTypeButton[@name="Login"]');
```

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `AGENTQ_TOKEN` | Your AgentQ API token | Yes |
| `AGENTQ_SERVICE_URL` | WebSocket service URL | No (defaults provided) |
| `AGENTQ_PROJECT_ID` | Project ID for test reporting | No |
| `AGENTQ_TESTRUN_ID` | Test run ID for result tracking | No |

## Supported Commands

The AI understands natural language commands for native mobile app automation:

### App Management
- "launch the app" / "open the app"
- "close the app" / "terminate the app"
- "background the app for 5 seconds"
- "bring app back to foreground"

### Native Mobile Gestures
- "tap on login button" (native tap)
- "long press on profile picture"
- "double tap on image to zoom"
- "swipe left/right/up/down"
- "scroll down to find element"
- "pinch to zoom in/out"
- "rotate device to landscape/portrait"

### Native Element Interactions
- "enter 'text' in the username field"
- "select 'option' from the picker"
- "toggle the switch on/off"
- "check the checkbox"
- "slide the slider to 75%"

### Device Interactions
- "hide the keyboard"
- "press back button"
- "press home button"
- "allow location permissions"
- "dismiss notification"

### Navigation Patterns
- "open navigation drawer"
- "tap on settings tab"
- "go back to previous screen"
- "close modal dialog"

### Assertions & Verification
- "verify that welcome message is displayed"
- "check if login button is enabled"
- "assert that user name contains 'John'"
- "verify app is on dashboard screen"

## API Reference

### Core Functions

#### `q(command: string): Promise<void>`
Execute a natural language command through AgentQ AI.

#### `initBrowser(capabilities: any): Promise<Browser>`
Initialize WebDriverIO browser with given capabilities.

#### `closeBrowser(): Promise<void>`
Close the browser session and clean up resources.

#### `runTest(title: string, capabilities: any, testFn: Function): Promise<void>`
Run a complete test with automatic setup, execution, and cleanup.

### Element Helper Functions

#### `$(selector: string): Element`
Get a single element using the current browser context. Equivalent to `browser.$(selector)`.

#### `$$(selector: string): Element[]`
Get multiple elements using the current browser context. Equivalent to `browser.$$(selector)`.

#### `getBrowser(): Browser`
Get the current WebDriverIO browser instance.

### Type Exports

#### `Browser`
WebDriverIO Browser type for TypeScript users.

```typescript
import { Browser } from 'agentq_mobile_automation_test';

function myFunction(browser: Browser) {
  // Your code here
}
```

## Scripts

```bash
# Build the library
npm run build

# Pull test cases from AgentQ platform
npm run pull-testcase

# Start Appium server
npm run start-appium
```

## Troubleshooting

### Common Issues

1. **Appium Connection Failed**
   - Ensure Appium server is running on port 4723
   - Check device/emulator is connected and accessible

2. **App Not Found**
   - Verify the app path in capabilities is correct
   - Ensure the app is compatible with the target device

3. **WebSocket Connection Issues**
   - Check AGENTQ_TOKEN is valid
   - Verify network connectivity to AgentQ service

### Debug Mode

Enable debug logging:
```bash
export DEBUG=agentq:*
```

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

MIT License - see LICENSE file for details.
