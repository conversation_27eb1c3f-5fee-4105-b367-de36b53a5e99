#!/bin/bash

# Load environment variables from .env.production file
if [ -f .env.production ]; then
  export $(grep -v '^#' .env.production | xargs)
fi

# Now build your Docker image, passing the loaded environment variables as build-args
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t asia-southeast2-docker.pkg.dev/agentq-464900/agentq/websocket_ai_single_test:1.2.0 \
  --build-arg NODE_ENV="${NODE_ENV}" \
  --build-arg JWT_SECRET="${JWT_SECRET}" \
  --build-arg AGENTQ_API_URL="${AGENTQ_API_URL}" \
  --build-arg PORT="${PORT}" \
  --build-arg LLM_PROVIDER="${LLM_PROVIDER}" \
  --build-arg GEMINI_API_KEY="${GEMINI_API_KEY}" \
  --build-arg GEMINI_MODEL="${GEMINI_MODEL}" \
  --build-arg OPENAI_API_KEY="${OPENAI_API_KEY}" \
  --build-arg OPENAI_MODEL="${OPENAI_MODEL}" \
  --build-arg CORE_SERVICE_URL="${CORE_SERVICE_URL}" \
  --build-arg GCP_PROJECT_ID="${GCP_PROJECT_ID}" \
  --build-arg GCP_CLIENT_EMAIL="${GCP_CLIENT_EMAIL}" \
  --build-arg GCP_PRIVATE_KEY="${GCP_PRIVATE_KEY}" \
  --build-arg GCP_BUCKET_NAME="${GCP_BUCKET_NAME}" \
  --build-arg ENABLE_CLOUD_STORAGE="${ENABLE_CLOUD_STORAGE}" \
  --build-arg AGENTQ_JWT_TOKEN="${AGENTQ_JWT_TOKEN}" \
  --build-arg REDIS_HOST="${REDIS_HOST}" \
  --build-arg REDIS_PORT="${REDIS_PORT}" \
  --build-arg REDIS_PASSWORD="${REDIS_PASSWORD}" \
  --build-arg REDIS_DB="${REDIS_DB}" \
  --build-arg AGENTQ_TOKEN="${AGENTQ_TOKEN}" \
  --build-arg AGENTQ_SERVICE_URL="${AGENTQ_SERVICE_URL}" \
  --push .


# docker buildx build \
  # --platform linux/amd64,linux/arm64 \
  # --build-arg NODE_ENV="${NODE_ENV}" \
  # --build-arg JWT_SECRET="${JWT_SECRET}" \
  # --build-arg AGENTQ_API_URL="${AGENTQ_API_URL}" \
  # --build-arg PORT="${PORT}" \
  # --build-arg LLM_PROVIDER="${LLM_PROVIDER}" \
  # --build-arg GEMINI_API_KEY="${GEMINI_API_KEY}" \
  # --build-arg GEMINI_MODEL="${GEMINI_MODEL}" \
  # --build-arg OPENAI_API_KEY="${OPENAI_API_KEY}" \
  # --build-arg OPENAI_MODEL="${OPENAI_MODEL}" \
  # --build-arg CORE_SERVICE_URL="${CORE_SERVICE_URL}" \
  # --build-arg GCP_PROJECT_ID="${GCP_PROJECT_ID}" \
  # --build-arg GCP_CLIENT_EMAIL="${GCP_CLIENT_EMAIL}" \
  # --build-arg GCP_PRIVATE_KEY="${GCP_PRIVATE_KEY}" \
  # --build-arg GCP_BUCKET_NAME="${GCP_BUCKET_NAME}" \
  # --build-arg ENABLE_CLOUD_STORAGE="${ENABLE_CLOUD_STORAGE}" \
  # --build-arg AGENTQ_JWT_TOKEN="${AGENTQ_JWT_TOKEN}" \
  # --build-arg REDIS_HOST="${REDIS_HOST}" \
  # --build-arg REDIS_PORT="${REDIS_PORT}" \
  # --build-arg REDIS_PASSWORD="${REDIS_PASSWORD}" \
  # --build-arg REDIS_DB="${REDIS_DB}" \
  # --build-arg AGENTQ_TOKEN="${AGENTQ_TOKEN}" \
  # --build-arg AGENTQ_SERVICE_URL="${AGENTQ_SERVICE_URL}" \
#   -t websocket_ai_single_test .


