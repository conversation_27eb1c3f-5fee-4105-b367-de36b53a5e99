#!/bin/bash

# Test script for simplified mobile automation
# This bypasses the agentq_mobile_automation_test library and uses direct WebDriverIO

echo "🚀 Starting simplified mobile test..."

# Clean up previous test artifacts
rm -rf client-apps/*

# Set test data
export TEST_CASE_ID="simplified-test-001"
export TEST_DATA='{
  "testCaseId": "simplified-test-001",
  "tcId": "1",
  "steps": [
    {
      "step": 1,
      "stepName": "Setup",
      "Actions": "[{\"action\":\"setup\",\"target\":\"\",\"value\":\"Android.SauceLabs.Mobile.Sample.app.2.7.1.apk\",\"fileUrl\":\"gs://agentq/test-data/mobile-app-files/f907b2b1-4347-480c-8bc5-0b669649599a/1755937959527-Android.SauceLabs.Mobile.Sample.app.2.7.1.apk\",\"deviceId\":\"0d625166-303c-5978-a9c0-302874e6feb4\",\"deviceName\":\"Medium_Phone_API_36.0\",\"platform\":\"android\",\"version\":\"16\"}]"
    },
    {
      "step": 2,
      "stepName": "Fill Username",
      "Actions": "[{\"action\":\"write\",\"target\":\"~test-Username\",\"value\":\"standard_user\"}]"
    },
    {
      "step": 3,
      "stepName": "Fill Password",
      "Actions": "[{\"action\":\"write\",\"target\":\"~test-Password\",\"value\":\"secret_sauce\"}]"
    },
    {
      "step": 4,
      "stepName": "Click Login",
      "Actions": "[{\"action\":\"click\",\"target\":\"~test-LOGIN\"}]"
    }
  ]
}'

echo "📱 Test data configured"
echo "🔧 Building project..."

# Build the project
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build completed"
echo "🧪 Running simplified test..."

# Run the simplified test
npx wdio run wdio.conf.ts --spec simplified-master.spec.ts

echo "🏁 Test execution completed"
