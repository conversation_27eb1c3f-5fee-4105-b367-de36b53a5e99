{"name": "agentq_web_automation_test", "version": "1.0.6", "description": "NPM library for no-code automation test from agents app", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/agentq-ai/agentq_web_automation_test.git"}, "keywords": ["agentq", "automation", "test"], "author": {"name": "agentq.id", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"axios": "^1.6.2", "dotenv": "^16.3.1", "ws": "^8.14.2"}, "devDependencies": {"@playwright/test": "^1.51.0", "@types/node": "^20.10.5", "@types/ws": "^8.5.10", "typescript": "^5.2.2"}, "peerDependencies": {"@playwright/test": "^1.51.0"}, "bugs": {"url": "https://github.com/agentq-ai/agentq_web_automation_test/issues"}, "homepage": "https://github.com/agentq-ai/agentq_web_automation_test#readme"}