# ---- Build Stage ----
FROM node:22 AS build

WORKDIR /app

# Install dependencies
ENV NPM_CONFIG_UPDATE_NOTIFIER=false
COPY package.json package-lock.json ./
COPY playwright.config.ts ./
RUN npm ci
RUN npx playwright install

# Copy source files
COPY . .

# Build TypeScript
RUN npm run build

# ---- Production Stage ----
FROM node:22-slim AS production

WORKDIR /app

ARG NODE_ENV
ARG JWT_SECRET
ARG AGENTQ_API_URL
ARG VITE_AI_SERVICE_URL
ARG PORT
ARG LLM_PROVIDER
ARG GEMINI_API_KEY
ARG GEMINI_MODEL
ARG OPENAI_API_KEY
ARG OPENAI_MODEL
ARG CORE_SERVICE_URL
ARG GCP_PROJECT_ID
ARG GCP_CLIENT_EMAIL
ARG GCP_PRIVATE_KEY
ARG GCP_BUCKET_NAME
ARG ENABLE_CLOUD_STORAGE
ARG AGENTQ_JWT_TOKEN
ARG REDIS_HOST
ARG REDIS_PORT
ARG REDIS_PASSWORD
ARG REDIS_DB
ARG AGENTQ_TOKEN
ARG AGENTQ_SERVICE_URL

ENV NODE_ENV=${NODE_ENV}
ENV JWT_SECRET=${JWT_SECRET}
ENV AGENTQ_API_URL=${AGENTQ_API_URL}
ENV VITE_AI_SERVICE_URL=${VITE_AI_SERVICE_URL}
ENV PORT=${PORT}
ENV LLM_PROVIDER=${LLM_PROVIDER}    
ENV GEMINI_API_KEY=${GEMINI_API_KEY}
ENV GEMINI_MODEL=${GEMINI_MODEL}
ENV OPENAI_API_KEY=${OPENAI_API_KEY}
ENV OPENAI_MODEL=${OPENAI_MODEL}
ENV CORE_SERVICE_URL=${CORE_SERVICE_URL}
ENV GCP_PROJECT_ID=${GCP_PROJECT_ID}
ENV GCP_CLIENT_EMAIL=${GCP_CLIENT_EMAIL}
ENV GCP_PRIVATE_KEY=${GCP_PRIVATE_KEY}
ENV GCP_BUCKET_NAME=${GCP_BUCKET_NAME}
ENV ENABLE_CLOUD_STORAGE=${ENABLE_CLOUD_STORAGE}
ENV AGENTQ_JWT_TOKEN=${AGENTQ_JWT_TOKEN}
ENV REDIS_HOST=${REDIS_HOST}
ENV REDIS_PORT=${REDIS_PORT}
ENV REDIS_PASSWORD=${REDIS_PASSWORD}
ENV REDIS_DB=${REDIS_DB}
ENV AGENTQ_TOKEN=${AGENTQ_TOKEN}
ENV AGENTQ_SERVICE_URL=${AGENTQ_SERVICE_URL}

# Install only production dependencies
COPY package.json package-lock.json ./
RUN npm ci --only=production

# Install Playwright system dependencies
RUN npx playwright install-deps

# Copy built files from build stage
COPY --from=build /app/dist ./dist
# Copy tests folder for Playwright
COPY --from=build /app/tests ./tests

# Copy Playwright configuration
COPY --from=build /app/playwright.config.ts ./playwright.config.ts

# Copy Playwright browser binaries
COPY --from=build /root/.cache/ms-playwright /root/.cache/ms-playwright

EXPOSE 3021

CMD ["node", "dist/server.js"]
