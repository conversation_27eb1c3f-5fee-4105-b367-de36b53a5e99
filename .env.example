# Server Configuration
PORT=3021

# Core Service URLs
CORE_SERVICE_URL=https://your-core-service-url.com

# JWT Configuration
JWT_SECRET=your-jwt-secret-here

# Redis Configuration for BullMQ
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# AgentQ Configuration
AGENTQ_SERVICE_URL=ws://localhost:3021
AGENTQ_ENV=development

# OpenAI Configuration (if using OpenAI for LLM)
OPENAI_API_KEY=your-openai-api-key

# Google AI Configuration (if using Google AI)
GOOGLE_AI_API_KEY=your-google-ai-api-key

# Google Cloud Storage Configuration
# Option 1: Use service account key file
# GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# Option 2: Use environment variables for service account
# GOOGLE_CLOUD_PROJECT_ID=your-project-id
# GOOGLE_CLOUD_CLIENT_EMAIL=<EMAIL>
# GOOGLE_CLOUD_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"

# Test Configuration
TEST_DATA={"steps":[...]}
CLIENT_ID=your-client-id
TEST_CASE_ID=your-test-case-id
