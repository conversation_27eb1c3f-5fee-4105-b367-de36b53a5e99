#!/usr/bin/env node
import axios, { AxiosResponse, AxiosError } from 'axios';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
dotenv.config();

interface AuthResponse {
  user: {
    id: string;
    email: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  };
  access_token: string;
}

interface TestCase {
  id: string;
  tcId: string;
  projectId: string;
  title: string;
  precondition: string;
  steps: string;
  expectation: string;
  createdAt: string;
  updatedAt: string;
}

interface Config {
  apiBaseUrl: string;
  projectId: string;
  testRunId: string;
  authEndpoint: string;
  authData: {
    email: string;
    password: string;
  };
  outputDir: string;
}

interface TestResult {
  id: string;
  testRunId: string;
  testCaseId: string;
  status: string;
  actualResult: string;
  executionTime: string | null;
  notes: string;
  screenshotUrl: string | null;
  videoUrl: string | null;
  createdAt: string;
  updatedAt: string;
  testCase: TestCase; // Assuming TestCase interface is already defined
}

interface TestSuiteResponse {
  results: TestResult[];
  total: number;
  totalPages: number;
}

// Configuration
const config: Config = {
  apiBaseUrl: `${process.env.AGENTQ_API_URL}`,
  projectId: `${process.env.AGENTQ_PROJECT_ID}`,
  testRunId: `${process.env.AGENTQ_TESTRUN_ID}`,
  authEndpoint: '/auth/login',
  authData: {
    email: '<EMAIL>',
    password: 'agentq'
  },
  outputDir: path.join(process.cwd(), 'tests')
};

// Parse command line arguments more robustly
function parseArgs(): { tcId: string | null; testRunId: string | null } {
  const args = process.argv.slice(2);
  let tcId: string | null = null;
  let testRunId: string | null = null;

  for (const arg of args) {
    const tcIdMatch = arg.match(/--tcid=(\d+)/);
    if (tcIdMatch) {
      tcId = tcIdMatch[1];
    }

    const testRunIdMatch = arg.match(/--testrunid=([\w-]+)/); // Allow alphanumeric and hyphens
    if (testRunIdMatch) {
      testRunId = testRunIdMatch[1];
    }
  }

  // Check environment variables as fallback
  if (!tcId && process.env.TCID) {
    tcId = process.env.TCID;
  }
  if (!testRunId && process.env.TESTRUNID) {
    testRunId = process.env.TESTRUNID;
  }

  return { tcId, testRunId };
}

const { tcId, testRunId } = parseArgs();

if (!tcId && !testRunId) {
  console.error('Error: Either --tcid or --testrunid is required.');
  console.error('Usage for test case:');
  console.error('  npm run agentq-pull-testcase -- --tcid=<number>');
  console.error('  or');
  console.error('  node agentq-pull-testcase.js --tcid=<number>');
  console.error('Usage for test suite:');
  console.error('  npm run agentq-pull-testsuite -- --testrunid=<uuid>');
  console.error('  or');
  console.error('  node agentq-pull-testcase.js -- --testrunid=<uuid>');
  process.exit(1);
}

// Create the output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

async function getAccessToken(): Promise<string> {
  try {
    console.log(`Authenticating with: ${config.apiBaseUrl}${config.authEndpoint}`);
    const response: AxiosResponse<AuthResponse> = await axios.post(
      `${config.apiBaseUrl}${config.authEndpoint}`,
      config.authData,
      {
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data.access_token;
  } catch (error: any) {
    console.error('Authentication failed:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
    } else if (error.request) {
      console.error('No response received during authentication. Check if the server is running.');
    } else {
      console.error(`Error during authentication: ${error.message}`);
    }
    process.exit(1);
  }
}

async function fetchTestCase(tcId: string, accessToken: string): Promise<TestCase> {
  try {
    console.log(`Fetching test case from: ${config.apiBaseUrl}/projects/${config.projectId}/test-cases/tcId/${tcId}`);
    const response: AxiosResponse<TestCase> = await axios.get(
      `${config.apiBaseUrl}/projects/${config.projectId}/test-cases/tcId/${tcId}`,
      {
        headers: {
          'accept': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        }
      }
    );
    return response.data;
  } catch (error: any) {
    console.error('API request failed:');

    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
    } else if (error.request) {
      console.error('No response received from the test case endpoint. Check if the server is running.');
    } else {
      console.error(`Error fetching test case: ${error.message}`);
    }

    process.exit(1);
  }
}

async function fetchTestSuite(testRunId: string, accessToken: string): Promise<TestSuiteResponse> {
  try {
    const apiUrl = `${config.apiBaseUrl}/projects/${config.projectId}/test-runs/${testRunId}/test-results?page=1&limit=10000`;
    console.log(`Fetching test suite results from: ${apiUrl}`);
    const response: AxiosResponse<TestSuiteResponse> = await axios.get(
      apiUrl,
      {
        headers: {
          'accept': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        }
      }
    );
    return response.data;
  } catch (error: any) {
    console.error(`Failed to fetch test suite results for testRunId: ${testRunId}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
    } else if (error.request) {
      console.error('No response received from the test suite endpoint. Check if the server is running.');
    } else {
      console.error(`Error fetching test suite: ${error.message}`);
    }
    process.exit(1);
  }
}

function generateTestSuiteSpecFile(testRunId: string, testResults: TestResult[]): string {
  const fileName = `testrun-${testRunId}.spec.ts`;
  const filePath = path.join(config.outputDir, fileName);

  const testsCode = testResults
    .map(result => {
      const sanitizedTitle = sanitizeFilename(result.testCase.title);
      const stepsCode = result.testCase.steps
        .split('\n')
        .filter(step => step.trim() !== '')
        .map(step => `    await q(\`${step}\`);`)
        .join('\n');

      return `  test(\`${result.testCase.tcId}-${sanitizedTitle}\`, async ({ page }) => {
${stepsCode}
  });`;
    })
    .join('\n\n');

  const fileContent = `import { q, test } from 'agentq-playwright';

test.describe('${testRunId}-testrun', () => {
${testsCode}
});
`;

  fs.writeFileSync(filePath, fileContent);
  console.log(`✅ Created: ${filePath}`);

  return filePath;
}

function sanitizeFilename(title: string): string {
  // Replace characters that are not allowed in filenames
  return title
    .replace(/[/\\?%*:|"<>]/g, '-')
    .replace(/\s+/g, '-')
    .substring(0, 100); // Limit length to avoid potential issues
}

function generateSpecFile(testCase: TestCase): string {
  const sanitizedTitle = sanitizeFilename(testCase.title);
  const fileName = `${sanitizedTitle}.spec.ts`;
  const filePath = path.join(config.outputDir, fileName);

  // Process precondition
  const preconditionCode = testCase.precondition
    ? testCase.precondition
      .split('\n')
      .filter(line => line.trim() !== '')
      .map(line => `  await q(\`User is on the ${line.trim()}.\`);`) // Adjusted for clarity
      .join('\n')
    : '';

  // Process steps
  const stepsCode = testCase.steps
    .split('\n')
    .filter(step => step.trim() !== '')
    .map(step => `  await q(\`${step}\`);`)
    .join('\n');

  // Process expected result
  const expectationCode = testCase.expectation
    ? testCase.expectation
      .split('\n')
      .filter(line => line.trim() !== '')
      .map(line => `  await q(\`${line.trim()}\`);`)
      .join('\n')
    : '';

  const fileContent = `import { q, test } from 'agentq-playwright';

test(\`${testCase.tcId}-${testCase.title}\`, async ({ page }) => {
  // Precondition:
${preconditionCode}
  // Steps:
${stepsCode}
  // Expected Result:
${expectationCode}
});
`;

  fs.writeFileSync(filePath, fileContent);
  console.log(`✅ Created: ${filePath}`);

  return filePath;
}

// Main execution
(async () => {
  if (tcId) {
    console.log(`🔍 Fetching test case with tcId: ${tcId}`);

    // Get the access token
    const accessToken = await getAccessToken();
    console.log(`🔑 Successfully obtained access token.`);

    // Fetch the test case using the access token
    const testCase = await fetchTestCase(tcId, accessToken);
    console.log(`📄 Found test case: ${testCase.title}`);

    // Generate the Playwright spec file
    const filePath = generateSpecFile(testCase);

    console.log(`\n🚀 Success! Test case converted to Playwright spec file.`);
  } else if (testRunId) {
    console.log(`🔍 Fetching test suite results for testRunId: ${testRunId}`);

    // Get the access token
    const accessToken = await getAccessToken();
    console.log(`🔑 Successfully obtained access token.`);

    // Fetch the test suite results
    const testSuiteResponse = await fetchTestSuite(testRunId, accessToken);
    const testResults = testSuiteResponse.results;
    console.log(`📄 Found ${testResults.length} test cases in test run: ${testRunId}`);

    // Generate the Playwright spec file for the test suite
    const filePath = generateTestSuiteSpecFile(testRunId, testResults);

    console.log(`\n🚀 Success! Test suite converted to Playwright spec file.`);
  }
})();