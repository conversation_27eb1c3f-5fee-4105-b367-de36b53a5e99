import { Page, expect } from '@playwright/test';
import { AICommand } from './types';

export class CommandExecutor {
  static async execute(command: AICommand, page: Page): Promise<void> {
    if (command.action === 'fill') {
      await page.waitForSelector(command.target, { timeout: 10000 });
      await page.fill(command.target, command.value as string);
    } else if (command.action === 'click') {
      await page.waitForSelector(command.target, { timeout: 10000 });
      await page.click(command.target);
    } else if (command.action === 'visit' || command.action === 'goto' || command.action === 'navigate' || command.action === 'open') {
      await page.goto(command.value as string, { timeout: 30000 });
    } else if (command.action === 'select') {
      await page.waitForSelector(command.target, { timeout: 10000 });
      if (typeof command.value === 'object' && command.value !== null && 'value' in command.value) {
        await page.selectOption(command.target, {
          value: command.value.value,
          label: command.value.label,
          index: command.value.index,
        });
      } else if (Array.isArray(command.value)) {
        await page.selectOption(command.target, command.value);
      } else if (typeof command.value === 'string') {
        await page.selectOption(command.target, command.value);
      } else if (command.value !== undefined) {
        throw new Error('command.value is not a string or an object with value, label, and index properties');
      } else {
        throw new Error('command.value is undefined');
      }
    } else if (command.action === 'check') {
      await page.waitForSelector(command.target, { timeout: 10000 });
      await page.check(command.target);
    } else if (command.action === 'uncheck') {
      await page.waitForSelector(command.target, { timeout: 10000 });
      await page.uncheck(command.target);
    } else if (command.action === 'type') {
      await page.waitForSelector(command.target, { timeout: 10000 });
      await page.type(command.target, command.value as string);
    } else if (command.action === 'wait') {
      await page.waitForTimeout(command.value as number);
    } else if (command.action === 'assertVisible') {
      await page.waitForSelector(command.target, { timeout: 10000, state: 'visible' });
    } else if (command.action === 'assertNotVisible') {
      await page.waitForSelector(command.target, { timeout: 10000, state: 'hidden' });
    } else if (command.action === 'assertEqual') {
      await page.waitForSelector(command.target, { timeout: 10000 });
      const textContent = await page.textContent(command.target);
      expect(textContent).toBe(command.value);
    } else if (command.action === 'assertContain') {
      await page.waitForSelector(command.target, { timeout: 10000 });
      const textContent = await page.textContent(command.target);
      expect(textContent).toContain(command.value as string);
    }
    // Add more command handling here as needed
  }
}