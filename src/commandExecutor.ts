import { <PERSON><PERSON><PERSON> } from 'webdriverio';
import { AICommand } from './types';

export class CommandExecutor {
  static async execute(command: AICommand, browser: Browser): Promise<void> {
    try {
      console.log(`🤖 Executing AI command: ${command.action} on ${command.target} with value: ${command.value}`);

      if (command.action === 'fill' || command.action === 'write') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        await element.setValue(command.value as string);
        console.log(`✅ Successfully filled/wrote "${command.value}" to ${command.target}`);
      } else if (command.action === 'click') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        await element.click();
      } else if (command.action === 'tap') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        await element.click(); // For native apps, click() works as tap
      } else if (command.action === 'visit' || command.action === 'goto' || command.action === 'navigate' || command.action === 'open') {
        // For native apps, this could mean launching app or navigating to a screen
        if (typeof command.value === 'string') {
          // Check if it's a URL (web context) or app activity/bundle
          if (command.value.startsWith('http')) {
            await browser.url(command.value);
          } else {
            // For native apps, this could be app launch or deep link
            try {
              await browser.execute('mobile: activateApp', { bundleId: command.value });
            } catch (error) {
              // Fallback: try as deep link or activity
              console.warn('Could not activate app, trying as deep link:', command.value);
              await browser.url(command.value);
            }
          }
        }
      } else if (command.action === 'select') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });

        // For native apps, selection is usually done by clicking
        if (typeof command.value === 'object' && command.value !== null && 'value' in command.value) {
          // Try to find and click the option
          const optionSelector = `*[contains(@text,'${command.value.value}') or contains(@content-desc,'${command.value.value}')]`;
          const option = browser.$(optionSelector);
          await option.waitForExist({ timeout: 5000 });
          await option.click();
        } else if (Array.isArray(command.value)) {
          for (const value of command.value) {
            const optionSelector = `*[contains(@text,'${value}') or contains(@content-desc,'${value}')]`;
            const option = browser.$(optionSelector);
            await option.waitForExist({ timeout: 5000 });
            await option.click();
          }
        } else if (typeof command.value === 'string') {
          const optionSelector = `*[contains(@text,'${command.value}') or contains(@content-desc,'${command.value}')]`;
          const option = browser.$(optionSelector);
          await option.waitForExist({ timeout: 5000 });
          await option.click();
        } else if (command.value !== undefined) {
          throw new Error('command.value is not a string or an object with value, label, and index properties');
        } else {
          throw new Error('command.value is undefined');
        }
      } else if (command.action === 'check') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        const isSelected = await element.isSelected();
        if (!isSelected) {
          await element.click();
        }
      } else if (command.action === 'uncheck') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        const isSelected = await element.isSelected();
        if (isSelected) {
          await element.click();
        }
      } else if (command.action === 'type') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        await element.addValue(command.value as string);
      } else if (command.action === 'wait') {
        await browser.pause(command.value as number);
      } else if (command.action === 'longPress') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        await element.touchAction([
          { action: 'press' },
          { action: 'wait', ms: 1000 },
          { action: 'release' }
        ]);
      } else if (command.action === 'doubleTap') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        await element.doubleClick();
      } else if (command.action === 'hideKeyboard') {
        try {
          await browser.hideKeyboard();
        } catch (error) {
          // Keyboard might not be visible, ignore error
          console.warn('Could not hide keyboard:', error);
        }
      } else if (command.action === 'pressKey') {
        if (typeof command.value === 'string') {
          // Convert string key names to key codes or use execute for key names
          await browser.execute('mobile: pressButton', { name: command.value });
        } else if (typeof command.value === 'number') {
          await browser.pressKeyCode(command.value);
        }
      } else if (command.action === 'launchApp' || command.action === 'activateApp') {
        if (typeof command.value === 'object' && command.value !== null && 'bundleId' in command.value) {
          const appValue = command.value as { bundleId?: string; appId?: string };
          const appId = appValue.bundleId || appValue.appId;
          if (appId) {
            await browser.execute('mobile: activateApp', { bundleId: appId });
          }
        } else if (typeof command.value === 'string') {
          await browser.execute('mobile: activateApp', { bundleId: command.value });
        }
      } else if (command.action === 'closeApp' || command.action === 'backgroundApp') {
        if (typeof command.value === 'object' && command.value !== null && 'bundleId' in command.value) {
          const appValue = command.value as { bundleId?: string; appId?: string };
          const appId = appValue.bundleId || appValue.appId;
          if (appId) {
            await browser.execute('mobile: terminateApp', { bundleId: appId });
          }
        } else if (typeof command.value === 'string') {
          await browser.execute('mobile: terminateApp', { bundleId: command.value });
        } else {
          // Background current app using mobile command
          await browser.execute('mobile: backgroundApp', { seconds: -1 });
        }
      } else if (command.action === 'swipe') {
        if (typeof command.value === 'object' && command.value !== null && 'direction' in command.value) {
          const swipeValue = command.value as { direction: string; distance?: number };
          const distance = swipeValue.distance || 500;

          const { width, height } = await browser.getWindowSize();
          const centerX = width / 2;
          const centerY = height / 2;

          let startX = centerX, startY = centerY, endX = centerX, endY = centerY;

          switch (swipeValue.direction.toLowerCase()) {
            case 'up':
              startY = centerY + distance / 2;
              endY = centerY - distance / 2;
              break;
            case 'down':
              startY = centerY - distance / 2;
              endY = centerY + distance / 2;
              break;
            case 'left':
              startX = centerX + distance / 2;
              endX = centerX - distance / 2;
              break;
            case 'right':
              startX = centerX - distance / 2;
              endX = centerX + distance / 2;
              break;
          }

          await browser.touchAction([
            { action: 'press', x: startX, y: startY },
            { action: 'wait', ms: 100 },
            { action: 'moveTo', x: endX, y: endY },
            { action: 'release' }
          ]);
        }
      } else if (command.action === 'pinch') {
        if (command.target) {
          const element = browser.$(command.target);
          await element.waitForExist({ timeout: 10000 });

          // Get element location and size for pinch gesture
          const location = await element.getLocation();
          const size = await element.getSize();
          const centerX = location.x + size.width / 2;
          const centerY = location.y + size.height / 2;

          // Pinch in gesture
          await browser.touchAction([
            { action: 'press', x: centerX - 50, y: centerY - 50 },
            { action: 'press', x: centerX + 50, y: centerY + 50 },
            { action: 'wait', ms: 100 },
            { action: 'moveTo', x: centerX - 10, y: centerY - 10 },
            { action: 'moveTo', x: centerX + 10, y: centerY + 10 },
            { action: 'release' },
            { action: 'release' }
          ]);
        }
      } else if (command.action === 'zoom') {
        if (command.target) {
          const element = browser.$(command.target);
          await element.waitForExist({ timeout: 10000 });

          // Get element location and size for zoom gesture
          const location = await element.getLocation();
          const size = await element.getSize();
          const centerX = location.x + size.width / 2;
          const centerY = location.y + size.height / 2;

          // Zoom out gesture
          await browser.touchAction([
            { action: 'press', x: centerX - 10, y: centerY - 10 },
            { action: 'press', x: centerX + 10, y: centerY + 10 },
            { action: 'wait', ms: 100 },
            { action: 'moveTo', x: centerX - 50, y: centerY - 50 },
            { action: 'moveTo', x: centerX + 50, y: centerY + 50 },
            { action: 'release' },
            { action: 'release' }
          ]);
        }
      } else if (command.action === 'scroll') {
        if (command.target) {
          const element = browser.$(command.target);
          await element.scrollIntoView();
        } else if (typeof command.value === 'object' && command.value !== null && 'direction' in command.value) {
          const scrollValue = command.value as { direction: string; distance?: number };
          const distance = scrollValue.distance || 300;

          switch (scrollValue.direction.toLowerCase()) {
            case 'up':
              await browser.execute('mobile: scroll', { direction: 'up', distance });
              break;
            case 'down':
              await browser.execute('mobile: scroll', { direction: 'down', distance });
              break;
          }
        }
      } else if (command.action === 'assertVisible') {
        const element = browser.$(command.target);
        await element.waitForDisplayed({ timeout: 10000 });
      } else if (command.action === 'assertNotVisible') {
        const element = browser.$(command.target);
        await element.waitForDisplayed({ timeout: 10000, reverse: true });
      } else if (command.action === 'assertEqual') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        const textContent = await element.getText();
        if (textContent !== command.value) {
          throw new Error(`Expected "${command.value}" but got "${textContent}"`);
        }
      } else if (command.action === 'assertContain') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        const textContent = await element.getText();
        if (!textContent.includes(command.value as string)) {
          throw new Error(`Expected text to contain "${command.value}" but got "${textContent}"`);
        }
      }
      // Add more command handling here as needed
    } catch (error) {
      console.error(`Error executing command ${command.action} on ${command.target}:`, error);
      throw error;
    }
  }
}