import { <PERSON>rowser, remote } from 'webdriverio';
import { WSClient } from './wsClient';
import { BrowserContext } from './pageContext';
import { config } from 'dotenv';
import { exportTestResult } from './testResult';
import { TestResult } from './types';
import { getAccessToken } from './testResult';

// Re-export WebDriverIO types for convenience
export { <PERSON>rows<PERSON> } from 'webdriverio';
export { BrowserContext } from './pageContext';

config();

export async function q(userPrompt: string): Promise<void> {
  const browser = BrowserContext.getInstance().getBrowser();
  return WSClient.getInstance().sendCommand(userPrompt, browser);
}

// WebDriverIO browser initialization function
export async function initBrowser(capabilities: any): Promise<Browser> {
  // Add default Appium server configuration
  const config = {
    ...capabilities,
    hostname: capabilities.hostname || 'localhost',
    port: capabilities.port || 4723,
    path: capabilities.path || '/wd/hub',
    logLevel: capabilities.logLevel || 'info'
  };
  
  const browser = await remote(config);
  BrowserContext.getInstance().setBrowser(browser);
  return browser;
}

// Helper function to close browser
export async function closeBrowser(): Promise<void> {
  await BrowserContext.getInstance().closeBrowser();
}

// Helper function to get element using current browser context
export function $(selector: string) {
  const browser = BrowserContext.getInstance().getBrowser();
  return browser.$(selector);
}

// Helper function to get multiple elements using current browser context
export function $$(selector: string) {
  const browser = BrowserContext.getInstance().getBrowser();
  return browser.$$(selector);
}

// Helper function to get current browser instance
export function getBrowser(): Browser {
  return BrowserContext.getInstance().getBrowser();
}

// WebDriverIO test framework integration
let cachedAccessToken: string | null = null;

export interface TestContext {
  browser: Browser;
  testTitle: string;
}

export async function runTest(
  testTitle: string,
  capabilities: any,
  testFunction: (context: TestContext) => Promise<void>
): Promise<void> {
  const startTime = Date.now();
  let browser: Browser | null = null;

  try {
    // Use existing browser session from WebDriverIO context (like successful approach)
    console.log('📱 Using existing browser session from WebDriverIO context');

    // Check if we're in a WebDriverIO test context with global browser
    if (typeof global !== 'undefined' && (global as any).browser) {
      browser = (global as any).browser;
      console.log('✅ Using global browser session from WebDriverIO');
      console.log(`🔍 Session ID: ${browser?.sessionId}`);
    } else {
      // Fallback: Initialize browser only if no global browser available
      console.log('⚠️ No global browser found, initializing new session...');
      browser = await initBrowser(capabilities);
    }

    if (!browser) {
      throw new Error('Failed to initialize browser session');
    }

    // Set browser context for q() function to work
    console.log('🔧 Setting browser context for q() function...');
    BrowserContext.getInstance().setBrowser(browser);
    console.log('✅ Browser context set successfully');

    // Get access token once before the test starts
    if (!cachedAccessToken) {
      cachedAccessToken = await getAccessToken();
      if (!cachedAccessToken) {
        console.warn('⚠️ No access token available - test result export will be skipped');
      }
    }

    // Run the test
    await testFunction({ browser, testTitle });

    // Extract tcId from test title (format: "12345-Test Title")
    const tcId = parseInt(testTitle.split('-')[0]);
    if (!isNaN(tcId) && process.env.AGENTQ_TESTRUN_ID && cachedAccessToken) {
      const executionTime = (Date.now() - startTime) / 1000;
      await exportTestResult(
        tcId.toString(),
        tcId.toString(),
        process.env.AGENTQ_TESTRUN_ID,
        {
          status: 'passed',
          actualResult: `Test "${testTitle}" passed successfully`,
          executionTime,
          notes: 'Test completed without errors'
        }
      );
    }
  } catch (error: any) {
    // Extract tcId from test title (format: "12345-Test Title")
    const tcId = parseInt(testTitle.split('-')[0]);
    if (!isNaN(tcId) && process.env.AGENTQ_TESTRUN_ID && cachedAccessToken) {
      const executionTime = (Date.now() - startTime) / 1000;
      await exportTestResult(
        tcId.toString(),
        tcId.toString(),
        process.env.AGENTQ_TESTRUN_ID,
        {
          status: 'failed',
          actualResult: error.message,
          executionTime,
          notes: `Test failed: ${error.message}`
        }
      );
    }
    throw error;
  } finally {
    // Clean up - only close session if we created it (not if using global browser)
    if (browser && !(typeof global !== 'undefined' && (global as any).browser === browser)) {
      console.log('🔚 Closing browser session that we created...');
      await closeBrowser();
      console.log('✅ Browser session closed');
    } else if (browser) {
      console.log('✅ Using global browser session - not closing');
    }
  }
}

// Simple assertion function for WebDriverIO
export function expect(actual: any) {
  return {
    toBe: (expected: any) => {
      if (actual !== expected) {
        throw new Error(`Expected "${expected}" but got "${actual}"`);
      }
    },
    toContain: (expected: any) => {
      if (!actual.includes(expected)) {
        throw new Error(`Expected "${actual}" to contain "${expected}"`);
      }
    },
    toBeDisplayed: async () => {
      if (actual && typeof actual.isDisplayed === 'function') {
        const isDisplayed = await actual.isDisplayed();
        if (!isDisplayed) {
          throw new Error('Element is not displayed');
        }
      } else {
        throw new Error('Invalid element for toBeDisplayed assertion');
      }
    }
  };
}