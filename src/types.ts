import { Page } from '@playwright/test';

export interface AICommand {
  action: 'fill' | 'click' | 'visit' | 'goto' | 'navigate' | 'open' | 'select' | 'check' | 'uncheck' | 'type' | 'wait' | 'assertVisible' | 'assertNotVisible' | 'assertEqual' | 'assertContain';
  target: string;
  value?: string | number | { value?: string; label?: string; index?: number } | string[];
}

export interface WSQueueItem {
  resolve: (value: void) => void;
  reject: (reason: any) => void;
  command: () => Promise<void>;
}

export interface Config {
  apiBaseUrl: string;
  projectId: string;
  authEndpoint: string;
  jwtToken: string | null;
  loginEndpoint: string;
  authData: {
    email: string;
    password: string;
  };
}

export interface TestResult {
  status: 'passed' | 'failed' | 'skipped';
  actualResult: string;
  executionTime: number;
  notes: string;
}

export interface TestResultResponse {
  id: string;
  testRunId: string;
  testCaseId: string;
  status: string;
  actualResult: string;
  executionTime: number;
  notes: string;
  screenshotUrl: string | null;
  videoUrl: string | null;
  createdAt: string;
  updatedAt: string;
  testCase: {
    id: string;
    tcId: number;
    title: string;
    precondition: string;
    steps: string;
    expectation: string;
    priority: string;
    type: string;
    platform: string;
    testCaseType: string;
    projectId: string;
    folderId: string;
    createdAt: string;
    updatedAt: string;
  };
}