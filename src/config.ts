import { config } from 'dotenv';

config();

interface AgentQConfig {
  TOKEN?: string;
  SERVICE_URL?: string;
}

export function getConfig(): AgentQConfig {
  // Get from environment variables only
  const envToken = process.env.AGENTQ_TOKEN || process.env.VITE_AGENTQ_API_KEY;
  const envServiceUrl = process.env.AGENTQ_SERVICE_URL || process.env.VITE_WEBSOCKET_URL;

  // Check if we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'staging' ||
        process.env.AGENTQ_ENV === 'development' ||
        envServiceUrl?.includes('localhost');

    // Determine SERVICE_URL based on environment
    let serviceUrl;
    if (isDevelopment) {
        // Development: use env variable or default to localhost:3021
        serviceUrl = envServiceUrl || 'ws://localhost:3021';
    } else {
        // Production: use env variable only (no fallback)
        serviceUrl = envServiceUrl;
    }

    // Return the configuration
    return {
        TOKEN: envToken,
        SERVICE_URL: serviceUrl,
    };
}

export const getServiceUrl = (): string => {
  const config = getConfig();

  // Check if we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'staging' ||
        process.env.AGENTQ_ENV === 'development' ||
        config.SERVICE_URL?.includes('localhost');
    // Default URLs based on environment
    const defaultServiceUrl = isDevelopment
        ? 'ws://localhost:3021'
        : 'wss://websocket-ai-automation-test-api.agentq.id';
    return config.SERVICE_URL || defaultServiceUrl;
};

export const getToken = (): string | undefined => {
  const config = getConfig();
  return config.TOKEN;
};
