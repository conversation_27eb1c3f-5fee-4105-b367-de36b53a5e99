import { <PERSON><PERSON><PERSON> } from 'webdriverio';

export class BrowserContext {
  private static instance: BrowserContext;
  private currentBrowser: Browser | null = null;

  private constructor() {}

  static getInstance(): BrowserContext {
    if (!BrowserContext.instance) {
      BrowserContext.instance = new BrowserContext();
    }
    return BrowserContext.instance;
  }

  setBrowser(browser: Browser): void {
    this.currentBrowser = browser;
  }

  getBrowser(): Browser {
    if (!this.currentBrowser) {
      throw new Error(
        'Browser context not set. Make sure to initialize the WebDriverIO browser instance before using AgentQ commands'
      );
    }
    return this.currentBrowser;
  }

  async closeBrowser(): Promise<void> {
    if (this.currentBrowser) {
      await this.currentBrowser.deleteSession();
      this.currentBrowser = null;
    }
  }
}

// Keep PageContext for backward compatibility but redirect to BrowserContext
export class PageContext {
  private static instance: PageContext;

  private constructor() {}

  static getInstance(): PageContext {
    if (!PageContext.instance) {
      PageContext.instance = new PageContext();
    }
    return PageContext.instance;
  }

  setPage(browser: Browser): void {
    BrowserContext.getInstance().setBrowser(browser);
  }

  getPage(): Browser {
    return BrowserContext.getInstance().getBrowser();
  }
}