import { Page } from '@playwright/test';

export class PageContext {
  private static instance: PageContext;
  private currentPage: Page | null = null;

  private constructor() {}

  static getInstance(): PageContext {
    if (!PageContext.instance) {
      PageContext.instance = new PageContext();
    }
    return PageContext.instance;
  }

  setPage(page: Page): void {
    this.currentPage = page;
  }

  getPage(): Page {
    if (!this.currentPage) {
      throw new Error(
        'Page context not set. Make sure to use the AgentQ test fixture by importing { test } from "agentq-playwright" instead of "@playwright/test"'
      );
    }
    return this.currentPage;
  }
}