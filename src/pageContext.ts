import { <PERSON><PERSON><PERSON> } from 'webdriverio';

export class PageContext {
  private static instance: PageContext;
  private browser: <PERSON>rows<PERSON> | null = null;

  private constructor() {}

  static getInstance(): PageContext {
    if (!PageContext.instance) {
      PageContext.instance = new PageContext();
    }
    return PageContext.instance;
  }

  setBrowser(browser: Browser): void {
    this.browser = browser;
  }

  getBrowser(): Browser {
    if (!this.browser) {
      throw new Error('Browser context not initialized. Make sure to call setBrowser() first.');
    }
    return this.browser;
  }

  hasBrowser(): boolean {
    return this.browser !== null;
  }

  clearBrowser(): void {
    this.browser = null;
  }
}
