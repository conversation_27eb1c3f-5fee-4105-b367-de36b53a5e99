#!/usr/bin/env node
import axios, { AxiosResponse } from 'axios';
import dotenv from 'dotenv';
import { TestResult, Config } from './types';

dotenv.config();

interface AuthResponse {
  access_token: string;
}

// Configuration
const config: Config = {
  apiBaseUrl: `${process.env.AGENTQ_API_URL}`,
  projectId: `${process.env.AGENTQ_PROJECT_ID}`,
  authEndpoint: '/auth/verify',
  jwtToken: process.env.AGENTQ_JWT_TOKEN || null,
  // Fallback to login if JWT token not provided
  loginEndpoint: '/auth/login',
  authData: {
    email: process.env.AGENTQ_AUTH_EMAIL || '<EMAIL>',
    password: process.env.AGENTQ_AUTH_PASSWORD || '<EMAIL>'
  }
};

let cachedToken: string | null = null;

export async function getAccessToken(): Promise<string> {
  if (cachedToken) {
    return cachedToken;
  }

  // Debug: Log JWT token availability
  console.log('🔍 JWT Token available:', !!config.jwtToken);
  console.log('🔍 JWT Token value:', config.jwtToken ? `${config.jwtToken.substring(0, 20)}...` : 'null');

  // Try JWT token verification first if token is provided
  if (config.jwtToken) {
    try {
      console.log(`Verifying JWT token with: ${config.apiBaseUrl}${config.authEndpoint}`);
      const response: AxiosResponse<any> = await axios.post(
        `${config.apiBaseUrl}${config.authEndpoint}`,
        {},
        {
          headers: {
            'accept': 'application/json',
            'Authorization': `Bearer ${config.jwtToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.valid) {
        console.log('✅ JWT token verification successful');
        cachedToken = config.jwtToken!; // We know it's not null here
        return cachedToken;
      }
    } catch (jwtError: any) {
      console.warn('JWT token verification failed, falling back to login:', jwtError.message);
    }
  }

  // Fallback to login with credentials
  if (config.authData && config.loginEndpoint) {
    try {
      console.log(`Authenticating with login: ${config.apiBaseUrl}${config.loginEndpoint}`);
      const response: AxiosResponse<AuthResponse> = await axios.post(
        `${config.apiBaseUrl}${config.loginEndpoint}`,
        config.authData,
        {
          headers: {
            'accept': 'application/json',
            'Content-Type': 'application/json'
          }
        }
      );
      cachedToken = response.data.access_token;
      return cachedToken;
    } catch (error: any) {
      console.error('Login authentication failed:');
      if (error.response) {
        console.error(`Status: ${error.response.status}`);
        console.error(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
      } else if (error.request) {
        console.error('No response received during authentication. Check if the server is running.');
      } else {
        console.error(`Error during authentication: ${error.message}`);
      }
      process.exit(1);
    }
  }

  console.error('No authentication method available (no JWT token or login credentials)');
  process.exit(1);
}

export async function exportTestResult(
  tcId: string,
  testCaseId: string,
  testRunId: string,
  result: Partial<TestResult>
): Promise<any> {
  const accessToken = await getAccessToken();
  const apiUrl = `${config.apiBaseUrl}/projects/${config.projectId}/test-runs/${testRunId}/test-results/tcId/${testCaseId}`;

  try {
    console.log(`Pushing test result to: ${apiUrl}`);
    const response = await axios.patch(
      apiUrl,
      result,
      {
        headers: {
          'accept': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ Test result pushed successfully');
    return response.data;
  } catch (error: any) {
    console.error('Failed to export test result:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
    } else if (error.request) {
      console.error('No response received while pushing test result. Check if the server is running.');
    } else {
      console.error(`Error pushing test result: ${error.message}`);
    }
    throw error;
  }
}