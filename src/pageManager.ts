import { Page } from '@playwright/test';

export class PageManager {
  private static instance: PageManager;
  private currentPage: Page | null = null;

  private constructor() {}

  static getInstance(): PageManager {
    if (!PageManager.instance) {
      PageManager.instance = new PageManager();
    }
    return PageManager.instance;
  }

  setCurrentPage(page: Page) {
    this.currentPage = page;
  }

  getCurrentPage(): Page {
    if (!this.currentPage) {
      throw new Error('No page is currently set. Make sure to call setCurrentPage() first.');
    }
    return this.currentPage;
  }
}