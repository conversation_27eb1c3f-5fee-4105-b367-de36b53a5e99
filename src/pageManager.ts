import { Browser } from 'webdriverio';

export class BrowserManager {
  private static instance: BrowserManager;
  private currentBrowser: <PERSON>rowser | null = null;

  private constructor() {}

  static getInstance(): BrowserManager {
    if (!BrowserManager.instance) {
      BrowserManager.instance = new BrowserManager();
    }
    return BrowserManager.instance;
  }

  setCurrentBrowser(browser: Browser) {
    this.currentBrowser = browser;
  }

  getCurrentBrowser(): Browser {
    if (!this.currentBrowser) {
      throw new Error('No browser is currently set. Make sure to call setCurrentBrowser() first.');
    }
    return this.currentBrowser;
  }

  async closeBrowser(): Promise<void> {
    if (this.currentBrowser) {
      await this.currentBrowser.deleteSession();
      this.currentBrowser = null;
    }
  }
}

// Keep PageManager for backward compatibility but redirect to BrowserManager
export class PageManager {
  private static instance: PageManager;

  private constructor() {}

  static getInstance(): PageManager {
    if (!PageManager.instance) {
      PageManager.instance = new PageManager();
    }
    return PageManager.instance;
  }

  setCurrentPage(browser: Browser) {
    BrowserManager.getInstance().setCurrentBrowser(browser);
  }

  getCurrentPage(): Browser {
    return BrowserManager.getInstance().getCurrentBrowser();
  }
}