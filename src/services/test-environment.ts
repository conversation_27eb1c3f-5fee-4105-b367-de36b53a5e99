/**
 * Test Environment Service for SaaS Mobile Testing
 * Manages dynamic environment variables for different test executions
 */

export interface TestEnvironment {
  TEST_PLATFORM?: string;
  TEST_DEVICE_NAME?: string;
  TEST_DEVICE_UDID?: string;
  TEST_PLATFORM_VERSION?: string;
  TEST_APP_URL?: string;
  CLIENT_ID?: string;
  TEST_CASE_ID?: string;
}

export class TestEnvironmentService {
  private static currentEnvironment: TestEnvironment = {};

  /**
   * Set environment variables for a specific test execution
   */
  static setTestEnvironment(testData: any): void {
    // Clear previous environment
    this.clearTestEnvironment();

    console.log('🔍 Debug - Received test data structure:', JSON.stringify(testData, null, 2));

    // Extract setup action from test data
    const setupAction = this.extractSetupAction(testData.steps || []);

    if (setupAction) {
      // Set mobile environment variables
      this.currentEnvironment = {
        TEST_PLATFORM: setupAction.platform,
        TEST_DEVICE_NAME: setupAction.deviceName,
        TEST_DEVICE_UDID: setupAction.deviceId,
        TEST_PLATFORM_VERSION: setupAction.version,
        TEST_APP_URL: setupAction.fileUrl || setupAction.value,
        CLIENT_ID: testData.clientId,
        TEST_CASE_ID: testData.testCaseId
      };

      // Set process environment variables
      Object.entries(this.currentEnvironment).forEach(([key, value]) => {
        if (value) {
          process.env[key] = value;
        }
      });

      console.log('📱 Mobile test environment configured:', this.currentEnvironment);
    } else {
      // For SaaS: Skip web testing, focus only on mobile apps
      console.log('⚠️ No mobile setup action found - skipping web test as requested');
      console.log('💡 For mobile testing, ensure your test data includes a setup action with platform, deviceName, and app details');

      // Set minimal environment for debugging
      this.currentEnvironment = {
        TEST_PLATFORM: 'mobile_required',
        CLIENT_ID: testData.clientId,
        TEST_CASE_ID: testData.testCaseId
      };

      // Set process environment variables
      Object.entries(this.currentEnvironment).forEach(([key, value]) => {
        if (value) {
          process.env[key] = value;
        }
      });

      console.log('🚫 Mobile-only mode: Web testing skipped');
    }
  }

  /**
   * Clear test environment variables
   */
  static clearTestEnvironment(): void {
    const envKeys = [
      'TEST_PLATFORM',
      'TEST_DEVICE_NAME', 
      'TEST_DEVICE_UDID',
      'TEST_PLATFORM_VERSION',
      'TEST_APP_URL',
      'CLIENT_ID',
      'TEST_CASE_ID'
    ];

    envKeys.forEach(key => {
      delete process.env[key];
    });

    this.currentEnvironment = {};
    console.log('🧹 Test environment cleared');
  }

  /**
   * Get current test environment
   */
  static getCurrentEnvironment(): TestEnvironment {
    return { ...this.currentEnvironment };
  }

  /**
   * Extract setup action from test steps
   */
  private static extractSetupAction(steps: any[]): any {
    for (const step of steps) {
      if (step.Actions) {
        try {
          const actions = JSON.parse(step.Actions);
          const setupAction = actions.find((action: any) => action.action === 'setup');
          if (setupAction) {
            return setupAction;
          }
        } catch (error) {
          console.error(`Failed to parse actions for step ${step.step}:`, error);
        }
      }
    }
    return null;
  }

  /**
   * Check if current environment is for mobile testing
   */
  static isMobileEnvironment(): boolean {
    const platform = this.currentEnvironment.TEST_PLATFORM;
    return platform === 'ios' || platform === 'android';
  }

  /**
   * Get test execution command with environment variables
   */
  static getTestCommand(testFilePath: string): string {
    if (this.isMobileEnvironment()) {
      return `npx wdio run wdio.conf.ts --spec "${testFilePath}"`;
    } else {
      return `npx wdio run wdio.conf.ts --spec "${testFilePath}"`;
    }
  }

  /**
   * Create test data object for WebSocket execution
   */
  static createTestExecutionData(originalTestData: any): any {
    return {
      ...originalTestData,
      environment: this.getCurrentEnvironment(),
      isMobile: this.isMobileEnvironment(),
      timestamp: new Date().toISOString()
    };
  }
}
