/**
 * Custom Video Recording Service for Mobile Tests
 * Handles video recording without causing synchronous termination issues
 */

import { spawn, ChildProcess } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';

export class VideoRecorder {
  private static instance: VideoRecorder;
  private recordingProcess: ChildProcess | null = null;
  private isRecording = false;
  private outputPath = '';

  private constructor() {}

  static getInstance(): VideoRecorder {
    if (!VideoRecorder.instance) {
      VideoRecorder.instance = new VideoRecorder();
    }
    return VideoRecorder.instance;
  }

  /**
   * Start video recording using adb screenrecord
   */
  async startRecording(testCaseId: string, deviceId: string = 'emulator-5554'): Promise<string> {
    if (this.isRecording) {
      console.warn('⚠️ Video recording already in progress');
      return this.outputPath;
    }

    try {
      // Create output directory
      const outputDir = process.env.CLIENT_ID ? `test-results/${process.env.CLIENT_ID}` : 'test-results';
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      // Generate unique filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      this.outputPath = path.join(outputDir, `video-${testCaseId}-${timestamp}.mp4`);

      console.log(`🎥 Starting video recording for test: ${testCaseId}`);
      console.log(`📁 Video will be saved to: ${this.outputPath}`);

      // Start recording using adb screenrecord
      this.recordingProcess = spawn('adb', [
        '-s', deviceId,
        'shell', 'screenrecord',
        '--verbose',
        '--bit-rate', '4000000',  // 4Mbps
        '--size', '720x1280',     // Reasonable resolution
        '/sdcard/test_recording.mp4'
      ]);

      this.recordingProcess.on('error', (error) => {
        console.error('❌ Video recording error:', error);
        this.cleanup();
      });

      this.isRecording = true;
      console.log('✅ Video recording started successfully');
      
      return this.outputPath;
    } catch (error) {
      console.error('❌ Failed to start video recording:', error);
      this.cleanup();
      throw error;
    }
  }

  /**
   * Stop video recording and pull the file from device
   */
  async stopRecording(deviceId: string = 'emulator-5554'): Promise<string | null> {
    if (!this.isRecording || !this.recordingProcess) {
      console.warn('⚠️ No active video recording to stop');
      return null;
    }

    try {
      console.log('🛑 Stopping video recording...');

      // Stop the recording process gracefully
      this.recordingProcess.kill('SIGINT');
      
      // Wait a moment for the recording to finalize
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Pull the video file from device
      console.log('📥 Pulling video file from device...');
      
      return new Promise((resolve, reject) => {
        const pullProcess = spawn('adb', [
          '-s', deviceId,
          'pull',
          '/sdcard/test_recording.mp4',
          this.outputPath
        ]);

        pullProcess.on('close', (code) => {
          if (code === 0) {
            console.log(`✅ Video saved successfully: ${this.outputPath}`);
            
            // Clean up the file from device
            spawn('adb', ['-s', deviceId, 'shell', 'rm', '/sdcard/test_recording.mp4']);
            
            this.cleanup();
            resolve(this.outputPath);
          } else {
            console.error(`❌ Failed to pull video file, exit code: ${code}`);
            this.cleanup();
            resolve(null);
          }
        });

        pullProcess.on('error', (error) => {
          console.error('❌ Error pulling video file:', error);
          this.cleanup();
          resolve(null);
        });
      });

    } catch (error) {
      console.error('❌ Error stopping video recording:', error);
      this.cleanup();
      return null;
    }
  }

  /**
   * Clean up recording state
   */
  private cleanup(): void {
    this.isRecording = false;
    this.recordingProcess = null;
    this.outputPath = '';
  }

  /**
   * Force stop recording (for emergency cleanup)
   */
  async forceStop(): Promise<void> {
    if (this.recordingProcess) {
      this.recordingProcess.kill('SIGKILL');
    }
    this.cleanup();
  }

  /**
   * Check if recording is active
   */
  isActivelyRecording(): boolean {
    return this.isRecording;
  }

  /**
   * Get current recording path
   */
  getCurrentRecordingPath(): string {
    return this.outputPath;
  }
}
