import WebSocket from 'ws';
import { <PERSON><PERSON><PERSON> } from 'webdriverio';
import { getServiceUrl, getToken } from './config';
import { WSQueueItem } from './types';
import { CommandExecutor } from './commandExecutor';

// Type alias for compatibility - in WebDriverIO context, Page is essentially Browser
type Page = Browser;

export class WSClient {
  private static instance: WSClient;
  private ws: WebSocket | null = null;
  private connected: boolean = false;
  private token: string;
  private commandQueue: WSQueueItem[] = [];

  private constructor() {
    // Try to get token from multiple sources
    this.token = getToken() ||
                 process.env.AGENTQ_TOKEN ||
                 process.env.VITE_AGENTQ_API_KEY ||
                 '';

    // Check if we're connecting to localhost (our local backend)
    const serviceUrl = getServiceUrl();
    const isLocalhost = serviceUrl.includes('localhost') || serviceUrl.includes('127.0.0.1');

    if (!this.token && !isLocalhost) {
      throw new Error(
        'AgentQ token is required. Please provide it via agentq.config.json, AGENTQ_TOKEN environment variable, or VITE_AGENTQ_API_KEY environment variable.'
      );
    }

    // For localhost, use a dummy token or empty string
    if (isLocalhost && !this.token) {
      this.token = 'localhost-development-token';
      console.log('🔧 Using localhost development mode - no authentication required');
    }

    this.connect();
  }

  static getInstance(): WSClient {
    if (!WSClient.instance) {
      WSClient.instance = new WSClient();
    }
    return WSClient.instance;
  }

  private connect() {
    if (this.ws) return;

    this.ws = new WebSocket(getServiceUrl());
    this.setupWebSocket();
  }

  private setupWebSocket() {
    if (!this.ws) return;

    this.ws.on('open', () => {
      this.connected = true;
      this.processQueue();
    });

    this.ws.on('close', () => {
      this.connected = false;
      this.ws = null;
      setTimeout(() => this.connect(), 5000);
    });

    this.ws.on('error', (error) => {
      console.error('WebSocket error:', error);
    });
  }

  private async processQueue() {
    while (this.commandQueue.length > 0) {
      const item = this.commandQueue.shift();
      if (item) {
        try {
          await item.command();
          item.resolve();
        } catch (error) {
          item.reject(error);
        }
      }
    }
  }

  async sendCommand(userPrompt: string, page: Page): Promise<void> {
    return new Promise((resolve, reject) => {
      const command = async () => {
        if (!this.ws) {
          throw new Error('WebSocket connection not available');
        }

        const pageSource = await page.getPageSource();

        return new Promise<void>((wsResolve, wsReject) => {
          const messageHandler = async (message: WebSocket.Data) => {
            try {
              const response = JSON.parse(message.toString());

              if (response.type === 'error') {
                wsReject(new Error(response.message));
                return;
              }

              if (response.command) {
                await CommandExecutor.execute(response.command, page);
                wsResolve();
              } else {
                wsReject(new Error('Invalid command received'));
              }

              this.ws?.removeListener('message', messageHandler);
            } catch (error) {
              wsReject(error);
            }
          };

          this.ws?.on('message', messageHandler);

          this.ws?.send(
            JSON.stringify({
              type: 'command',
              prompt: userPrompt,
              pageSource,
              token: this.token,
            })
          );
        });
      };

      if (this.connected) {
        command().then(resolve).catch(reject);
      } else {
        this.commandQueue.push({ resolve, reject, command });
      }
    });
  }
}
