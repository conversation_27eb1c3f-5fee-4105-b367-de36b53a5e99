import WebSocket from 'ws';
import { Page } from '@playwright/test';
import { getServiceUrl, getToken } from './config';
import { AICommand, WSQueueItem } from './types';
import { CommandExecutor } from './commandExecutor';

export class WSClient {
  private static instance: WSClient;
  private ws: WebSocket | null = null;
  private connected: boolean = false;
  private token: string;
  private commandQueue: WSQueueItem[] = [];

  private constructor() {
    // Try to get token from multiple sources
    this.token = getToken() || 
                 process.env.AGENTQ_TOKEN || 
                 process.env.VITE_AGENTQ_API_KEY || 
                 '';
                 
    if (!this.token) {
      throw new Error(
        'AgentQ token is required. Please provide it via agentq.config.json, AGENTQ_TOKEN environment variable, or VITE_AGENTQ_API_KEY environment variable.'
      );
    }
    this.connect();
  }

  static getInstance(): WSClient {
    if (!WSClient.instance) {
      WSClient.instance = new WSClient();
    }
    return WSClient.instance;
  }

  private connect() {
    if (this.ws) return;

    this.ws = new WebSocket(getServiceUrl());
    this.setupWebSocket();
  }

  private setupWebSocket() {
    if (!this.ws) return;

    this.ws.on('open', () => {
      this.connected = true;
      this.processQueue();
    });

    this.ws.on('close', () => {
      this.connected = false;
      this.ws = null;
      setTimeout(() => this.connect(), 5000);
    });

    this.ws.on('error', (error) => {
      console.error('WebSocket error:', error);
    });
  }

  private async processQueue() {
    while (this.commandQueue.length > 0) {
      const item = this.commandQueue.shift();
      if (item) {
        try {
          await item.command();
          item.resolve();
        } catch (error) {
          item.reject(error);
        }
      }
    }
  }

  async sendCommand(userPrompt: string, page: Page): Promise<void> {
    return new Promise((resolve, reject) => {
      const command = async () => {
        if (!this.ws) {
          throw new Error('WebSocket connection not available');
        }

        const pageSource = await page.content();

        return new Promise<void>((wsResolve, wsReject) => {
          const messageHandler = async (message: WebSocket.Data) => {
            try {
              const response = JSON.parse(message.toString());

              if (response.type === 'error') {
                wsReject(new Error(response.message));
                return;
              }

              if (response.command) {
                await CommandExecutor.execute(response.command, page);
                wsResolve();
              } else {
                wsReject(new Error('Invalid command received'));
              }

              this.ws?.removeListener('message', messageHandler);
            } catch (error) {
              wsReject(error);
            }
          };

          this.ws?.on('message', messageHandler);

          this.ws?.send(
            JSON.stringify({
              type: 'command',
              prompt: userPrompt,
              pageSource,
              token: this.token,
            })
          );
        });
      };

      if (this.connected) {
        command().then(resolve).catch(reject);
      } else {
        this.commandQueue.push({ resolve, reject, command });
      }
    });
  }
}
