import { 
  q, 
  initBrowser, 
  closeBrowser, 
  $, 
  $$, 
  getBrowser, 
  runTest 
} from 'agentq_mobile_automation_test';

// Native Android app capabilities
const androidNativeCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:app': '/path/to/your/app.apk',
  'appium:automationName': 'UiAutomator2',
  'appium:newCommandTimeout': 300,
  'appium:autoGrantPermissions': true,
  'appium:noReset': false,
  'appium:fullReset': false,
  'appium:unicodeKeyboard': true,
  'appium:resetKeyboard': true
};

// Native iOS app capabilities
const iosNativeCapabilities = {
  platformName: 'iOS',
  'appium:deviceName': 'iPhone 14',
  'appium:app': '/path/to/your/app.ipa',
  'appium:automationName': 'XCUITest',
  'appium:newCommandTimeout': 300,
  'appium:autoAcceptAlerts': true,
  'appium:noReset': false,
  'appium:wdaLaunchTimeout': 60000
};

async function nativeMobileAppTest() {
  console.log('Starting native mobile app test...');
  
  // Use Android capabilities (change to iosNativeCapabilities for iOS)
  const browser = await initBrowser(androidNativeCapabilities);
  
  try {
    // 1. App Launch and Initial Setup
    await q("wait for app to launch completely");
    await q("dismiss any welcome screens or tutorials");
    
    // 2. Native Mobile Gestures
    console.log('Testing native mobile gestures...');
    
    // Tap gesture (native mobile specific)
    await q("tap on the main menu button");
    
    // Long press gesture
    await q("long press on the settings icon");
    
    // Swipe gestures for navigation
    await q("swipe left to go to next screen");
    await q("swipe right to go back");
    await q("swipe up to scroll down");
    await q("swipe down to scroll up");
    
    // 3. Native Element Interactions
    console.log('Testing native element interactions...');
    
    // Using accessibility IDs (recommended for native apps)
    const loginButton = $('~loginButton'); // Accessibility ID
    await loginButton.waitForDisplayed();
    await loginButton.click();
    
    // Using XPath for complex element selection
    const usernameField = $('//android.widget.EditText[@content-desc="username"]');
    await usernameField.setValue('<EMAIL>');
    
    // Using class name and text (Android specific)
    const submitButton = $('//android.widget.Button[@text="Submit"]');
    await submitButton.click();
    
    // 4. Native App Specific Actions
    console.log('Testing native app specific actions...');
    
    // Hide keyboard (mobile specific)
    await q("hide the keyboard");
    
    // App state management
    await q("background the app for 3 seconds");
    await q("bring app back to foreground");
    
    // Device interactions
    await q("rotate device to landscape");
    await q("rotate device back to portrait");
    
    // 5. Native Form Interactions
    console.log('Testing native form interactions...');
    
    // Native text input
    const emailField = $('~emailField');
    await emailField.setValue('<EMAIL>');
    
    // Native dropdown/picker selection
    const countryPicker = $('~countryPicker');
    await countryPicker.click();
    await q("select 'United States' from the country list");
    
    // Native checkbox/switch
    const agreeCheckbox = $('~agreeToTerms');
    await agreeCheckbox.click();
    
    // Native date picker
    const datePicker = $('~birthDatePicker');
    await datePicker.click();
    await q("select date January 15, 1990");
    
    // 6. Native Navigation Patterns
    console.log('Testing native navigation patterns...');
    
    // Tab navigation
    await q("tap on the profile tab");
    await q("tap on the settings tab");
    await q("tap on the home tab");
    
    // Drawer navigation
    await q("open the navigation drawer");
    await q("tap on account settings");
    await q("go back to main screen");
    
    // Modal/Dialog interactions
    await q("tap on delete account button");
    await q("tap cancel on the confirmation dialog");
    
    // 7. Native Assertions and Validations
    console.log('Testing native assertions...');
    
    // Check element visibility
    const welcomeMessage = $('~welcomeMessage');
    await welcomeMessage.waitForDisplayed();
    
    // Verify text content
    const userNameLabel = $('~userNameLabel');
    const userName = await userNameLabel.getText();
    console.log('Current user:', userName);
    
    // Check element states
    const saveButton = $('~saveButton');
    const isEnabled = await saveButton.isEnabled();
    console.log('Save button enabled:', isEnabled);
    
    // 8. Advanced Native Features
    console.log('Testing advanced native features...');
    
    // Camera/Photo picker simulation
    await q("tap on profile picture");
    await q("select take photo option");
    await q("allow camera permissions if prompted");
    
    // Location services
    await q("tap on find nearby stores");
    await q("allow location permissions if prompted");
    
    // Push notifications (if testable)
    await q("enable push notifications");
    
    // 9. Performance and Memory Testing
    console.log('Testing app performance...');
    
    // Stress test with multiple actions
    for (let i = 0; i < 5; i++) {
      await q(`navigate to screen ${i + 1}`);
      await q("perform some actions on the screen");
      await q("go back to previous screen");
    }
    
    // Memory cleanup
    await q("clear app cache if available");
    
    console.log('Native mobile app test completed successfully!');
    
  } catch (error) {
    console.error('Native mobile app test failed:', error);
    
    // Take screenshot on failure
    const browser = getBrowser();
    await browser.saveScreenshot('./native-app-error-screenshot.png');
    
    throw error;
  } finally {
    await closeBrowser();
    console.log('Browser session closed');
  }
}

// Test using the runTest framework
async function frameworkBasedNativeTest() {
  await runTest('Native-App-Login-Test', androidNativeCapabilities, async ({ browser }) => {
    // AI-powered test steps
    await q("launch the app and wait for it to load");
    await q("tap on the login button");
    await q("enter '<EMAIL>' in the email field");
    await q("enter 'password123' in the password field");
    await q("tap the submit button");
    await q("verify that the dashboard screen is displayed");
    await q("verify that the user name 'Test User' is shown");
    
    // Direct element verification
    const dashboardTitle = $('~dashboardTitle');
    await dashboardTitle.waitForDisplayed();
    const titleText = await dashboardTitle.getText();
    console.log('Dashboard title:', titleText);
  });
}

// Selector examples for native apps
function nativeSelectorExamples() {
  // Android selectors
  const androidSelectors = {
    // Accessibility ID (recommended)
    loginButton: $('~loginButton'),
    
    // Resource ID
    usernameField: $('android=new UiSelector().resourceId("com.app:id/username")'),
    
    // Text content
    submitButton: $('android=new UiSelector().text("Submit")'),
    
    // Class name
    editText: $('android.widget.EditText'),
    
    // XPath
    specificButton: $('//android.widget.Button[@text="Login" and @enabled="true"]'),
    
    // Content description
    menuIcon: $('android=new UiSelector().description("Menu")')
  };
  
  // iOS selectors
  const iosSelectors = {
    // Accessibility ID (recommended)
    loginButton: $('~loginButton'),
    
    // Predicate string
    usernameField: $('-ios predicate string:name == "username" AND type == "XCUIElementTypeTextField"'),
    
    // Class chain
    submitButton: $('-ios class chain:**/XCUIElementTypeButton[`name == "Submit"`]'),
    
    // XPath
    specificButton: $('//XCUIElementTypeButton[@name="Login" and @enabled="true"]')
  };
  
  return { androidSelectors, iosSelectors };
}

// Run the examples
async function runNativeExamples() {
  try {
    await nativeMobileAppTest();
    console.log('\n---\n');
    await frameworkBasedNativeTest();
  } catch (error) {
    console.error('Native mobile app examples failed:', error);
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  runNativeExamples();
}

export { 
  nativeMobileAppTest, 
  frameworkBasedNativeTest, 
  nativeSelectorExamples,
  androidNativeCapabilities,
  iosNativeCapabilities 
};
