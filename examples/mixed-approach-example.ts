import { 
  q, 
  initBrowser, 
  closeBrowser, 
  $, 
  $$, 
  get<PERSON><PERSON><PERSON>, 
  <PERSON>rows<PERSON> 
} from 'agentq_mobile_automation_test';

// Example capabilities for Android
const androidCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:app': '/path/to/your/app.apk',
  'appium:automationName': 'UiAutomator2',
  'appium:newCommandTimeout': 300
};

async function mixedApproachExample() {
  console.log('Starting mixed approach test...');
  
  // Initialize browser
  const browser = await initBrowser(androidCapabilities);
  
  try {
    // 1. Use AI command to open app
    await q("open the app and wait for it to load");
    
    // 2. Use direct WebDriverIO element access for precise control
    const loginButton = $('~loginButton'); // Using accessibility ID
    await loginButton.waitForDisplayed({ timeout: 10000 });
    
    // Check if element exists before interacting
    if (await loginButton.isDisplayed()) {
      await loginButton.click();
      console.log('Login button clicked using direct element access');
    }
    
    // 3. Mix AI and direct element access
    await q("wait for login form to appear");
    
    // Use direct element access for form fields
    const usernameField = $('#username');
    const passwordField = $('#password');
    
    await usernameField.waitForDisplayed();
    await usernameField.setValue('<EMAIL>');
    
    await passwordField.waitForDisplayed();
    await passwordField.setValue('securepassword123');
    
    console.log('Form fields filled using direct element access');
    
    // 4. Use AI for complex interactions
    await q("tap the submit button and wait for navigation");
    
    // 5. Use direct element access for assertions
    const welcomeMessage = $('~welcomeMessage');
    await welcomeMessage.waitForDisplayed({ timeout: 15000 });
    
    const messageText = await welcomeMessage.getText();
    console.log('Welcome message:', messageText);
    
    // 6. Use $$ for multiple elements
    const menuItems = $$('.menu-item');
    const menuCount = await menuItems.length;
    console.log(`Found ${menuCount} menu items`);
    
    // 7. Use AI for navigation
    await q("navigate to settings page");
    
    // 8. Use direct browser access for advanced operations
    const currentBrowser = getBrowser();
    const currentUrl = await currentBrowser.getUrl();
    console.log('Current URL:', currentUrl);
    
    // 9. Take screenshot using direct browser access
    await currentBrowser.saveScreenshot('./test-screenshot.png');
    console.log('Screenshot saved');
    
    // 10. Use AI for final verification
    await q("verify that settings page is displayed correctly");
    
    console.log('Mixed approach test completed successfully!');
    
  } catch (error) {
    console.error('Test failed:', error);
    
    // Take screenshot on failure
    const browser = getBrowser();
    await browser.saveScreenshot('./error-screenshot.png');
    
    throw error;
  } finally {
    // Clean up
    await closeBrowser();
    console.log('Browser closed');
  }
}

// Helper function to demonstrate element utilities
async function elementUtilitiesExample() {
  console.log('Demonstrating element utilities...');
  
  const browser = await initBrowser(androidCapabilities);
  
  try {
    await q("open the app");
    
    // Single element selection
    const button = $('button[type="submit"]');
    await button.waitForDisplayed();
    
    // Multiple elements selection
    const allButtons = $$('button');
    console.log(`Found ${await allButtons.length} buttons`);
    
    // Element properties and methods
    const isButtonEnabled = await button.isEnabled();
    const buttonText = await button.getText();
    const buttonSize = await button.getSize();
    
    console.log('Button info:', {
      enabled: isButtonEnabled,
      text: buttonText,
      size: buttonSize
    });
    
    // Element interactions
    if (isButtonEnabled) {
      await button.click();
    }
    
  } finally {
    await closeBrowser();
  }
}

// Run the examples
async function runExamples() {
  try {
    await mixedApproachExample();
    console.log('\n---\n');
    await elementUtilitiesExample();
  } catch (error) {
    console.error('Example failed:', error);
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  runExamples();
}

export { mixedApproachExample, elementUtilitiesExample };
